import os
import json
import re
import string
import time
import tempfile
import logging
from collections import Counter
from io import BytesIO
import speech_recognition as sr
from django.conf import settings
from django.http import HttpResponse, HttpResponseBadRequest, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.csrf import csrf_exempt
from dotenv import load_dotenv

# Charger les variables d'environnement dès le début
load_dotenv()
from moviepy import VideoFileClip
from nltk.corpus import stopwords
from nltk.tokenize import sent_tokenize, word_tokenize
from openai import OpenAI
from pydub import AudioSegment
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import inch
from reportlab.lib.utils import simpleSplit
from reportlab.pdfgen import canvas
from sumy.nlp.stemmers import Stemmer
from sumy.nlp.tokenizers import Tokenizer
from sumy.parsers.plaintext import PlaintextParser
from sumy.summarizers.lsa import LsaSummarizer

# Imports pour les documents
try:
    from PyPDF2 import PdfReader
except ImportError:
    try:
        from PyPDF2 import PdfFileReader as PdfReader
    except ImportError:
        PdfReader = None

try:
    import docx
except ImportError:
    docx = None

from .forms import TeleversementFichierForm
from .models import Fichier, Resume, Transcription, ChatMessage, MotsCles
from django.http import JsonResponse
from django.views.decorators.http import require_POST
import logging
from django.conf import settings

from django.http import JsonResponse
import whisper
import tempfile
from django.contrib.auth import logout
from .models import Resume
from django.contrib.auth.decorators import user_passes_test, login_required
from django.contrib import messages
from django.views.decorators.csrf import csrf_exempt
import requests
from nltk.tokenize import word_tokenize
import nltk
from datetime import datetime
nltk.download('punkt')
nltk.download('stopwords')



def export_resume_analysis_txt(request):
    if not request.user.is_superuser:
        return HttpResponse("Accès refusé", status=403)

    # Simuler les données d’analyse
    mots_cles = ["apprentissage", "modèle", "intelligence", "données"]
    mots_repetitifs = {"modèle": 5, "données": 4}
    resume_count = 12

    # Créer le contenu du rapport
    contenu = f"""Rapport d'analyse des résumés - {datetime.now().strftime('%d/%m/%Y %H:%M')}

Nombre total de résumés analysés : {resume_count}

Mots-clés extraits :
- {', '.join(mots_cles)}

Mots les plus répétés :
"""
    for mot, freq in mots_repetitifs.items():
        contenu += f"- {mot} : {freq} occurrences\n"

    # Créer la réponse HTTP avec le bon type MIME
    response = HttpResponse(contenu, content_type='text/plain')
    response['Content-Disposition'] = 'attachment; filename="rapport_resume.txt"'
    return response
def is_admin(user):
    return user.is_superuser
@user_passes_test(is_admin)
def admin_resume_analysis(request):
    resumes = Resume.objects.all()
    keywords_report = analyze_resumes(resumes)
    return render(request, 'admin_resume_analysis.html', {
        'resumes': resumes,
        'keywords_report': keywords_report
    })

#analyser le texte
def analyze_resumes(resumes):
    all_text = " ".join([r.resume_text for r in resumes])
    all_text = re.sub(r'[^\w\s]', '', all_text).lower()
    words = word_tokenize(all_text)
    words = [w for w in words if w not in stopwords.words('french') and len(w) > 2]
    word_freq = Counter(words)
    return word_freq.most_common(20)

def logout_view(request):
    logout(request)
    return redirect('accueil')
def transcrire(request):
    if request.method == 'POST':
        fichier = request.FILES.get('fichier')
        if not fichier:
            return HttpResponse("Aucun fichier envoyé.", status=400)

        # Sauvegarder temporairement le fichier
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(fichier.name)[1]) as temp_file:
            for chunk in fichier.chunks():
                temp_file.write(chunk)
            temp_path = temp_file.name

        try:
            # Charger le modèle Whisper (choix en fonction du formulaire)
            qualite = request.POST.get('qualite', 'base')
            if qualite == 'standard':
                model = whisper.load_model("base")
            elif qualite == 'premium':
                model = whisper.load_model("large")
            else:
                model = whisper.load_model("medium")

            langue = request.POST.get("langue", "fr")
            if langue == "auto":
                result = model.transcribe(temp_path)
            else:
                result = model.transcribe(temp_path, language=langue)

            transcription = result["text"]
            return render(request, "transcription_result.html", {"transcription": transcription})

        finally:
            os.remove(temp_path)

    return render(request, "formulaire_transcription.html")

logger = logging.getLogger(__name__)
def handle_uploaded_file(f):
    file_path = os.path.join(settings.MEDIA_ROOT, f.name)
    with open(file_path, 'wb+') as destination:
        for chunk in f.chunks():
            destination.write(chunk)
    return file_path

def verifier_ffmpeg():
    """Vérifie si FFmpeg est disponible sur le système."""
    import subprocess
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True, text=True)
        logger.info("✅ FFmpeg disponible")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        logger.error(f"❌ FFmpeg non disponible: {e}")
        return False


def convertir_mp3_en_wav(chemin_mp3):
    """
    Convertit un fichier MP3 en WAV pour la reconnaissance vocale.

    Args:
        chemin_mp3 (str): Chemin vers le fichier MP3

    Returns:
        str: Chemin vers le fichier WAV créé, ou None en cas d'erreur
    """
    try:
        print(f"🎵 Conversion MP3 vers WAV: {chemin_mp3}")

        # Vérifier que le fichier MP3 existe
        if not os.path.exists(chemin_mp3):
            print(f"❌ Fichier MP3 introuvable: {chemin_mp3}")
            return None

        # Vérifier la taille du fichier
        file_size = os.path.getsize(chemin_mp3)
        print(f"📊 Taille fichier MP3: {file_size} bytes")

        if file_size == 0:
            print("❌ Fichier MP3 vide")
            return None

        # Créer le chemin WAV dans le même répertoire
        chemin_wav = chemin_mp3.replace(".mp3", "_converted.wav")
        print(f"📁 Fichier WAV cible: {chemin_wav}")

        # Charger et convertir l'audio
        print("🔄 Chargement du fichier MP3...")
        audio = AudioSegment.from_mp3(chemin_mp3)

        print(f"📊 Durée audio: {len(audio)/1000:.2f} secondes")

        # Exporter en WAV avec paramètres optimisés pour la reconnaissance vocale
        print("🔄 Export en WAV...")
        audio.export(
            chemin_wav,
            format="wav",
            parameters=["-ar", "16000", "-ac", "1"]  # 16kHz mono
        )

        # Vérifier que le fichier WAV a été créé
        if os.path.exists(chemin_wav) and os.path.getsize(chemin_wav) > 0:
            print(f"✅ Conversion réussie: {chemin_wav}")
            return chemin_wav
        else:
            print("❌ Fichier WAV non créé ou vide")
            return None

    except Exception as e:
        print(f"❌ Erreur lors de la conversion MP3 en WAV: {e}")
        print(f"🔍 Type d'erreur: {type(e).__name__}")

        # Essayer une méthode alternative
        try:
            print("🔄 Tentative de conversion alternative...")
            chemin_wav_alt = chemin_mp3.replace(".mp3", "_alt.wav")

            # Conversion simple sans paramètres
            audio = AudioSegment.from_mp3(chemin_mp3)
            audio.export(chemin_wav_alt, format="wav")

            if os.path.exists(chemin_wav_alt) and os.path.getsize(chemin_wav_alt) > 0:
                print(f"✅ Conversion alternative réussie: {chemin_wav_alt}")
                return chemin_wav_alt
            else:
                print("❌ Conversion alternative échouée")
                return None

        except Exception as e2:
            print(f"❌ Conversion alternative échouée: {e2}")
            return None


def convertir_audio_format_compatible(audio_path_input, audio_path_output):
    """Convertit un fichier audio au format compatible avec Google Speech Recognition."""
    import subprocess

    try:
        print(f"Conversion du format audio: {audio_path_input} -> {audio_path_output}")

        # Commande FFmpeg pour convertir au format compatible
        cmd = [
            'ffmpeg',
            '-i', audio_path_input,
            '-acodec', 'pcm_s16le',  # Codec WAV 16-bit
            '-ar', '16000',          # Fréquence 16kHz
            '-ac', '1',              # Mono
            '-y',                    # Écraser le fichier de sortie
            audio_path_output
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        if result.returncode == 0 and os.path.exists(audio_path_output):
            file_size = os.path.getsize(audio_path_output)
            if file_size > 0:
                print(f"Conversion réussie: {file_size} bytes")
                return True

        print(f"Échec conversion: code {result.returncode}")
        return False

    except Exception as e:
        print(f"Erreur conversion format: {e}")
        return False


def test_conversion_simple(video_path, audio_path):
    """Test de conversion simple pour diagnostic."""
    import subprocess

    print(f"🧪 Test conversion simple")
    print(f"📁 Vidéo: {video_path}")
    print(f"📁 Audio: {audio_path}")

    # Vérifications préliminaires
    if not os.path.exists(video_path):
        print(f"❌ Fichier vidéo inexistant: {video_path}")
        return False

    video_size = os.path.getsize(video_path)
    print(f"📊 Taille vidéo: {video_size} bytes")

    # Créer le répertoire de destination
    audio_dir = os.path.dirname(audio_path)
    if not os.path.exists(audio_dir):
        os.makedirs(audio_dir, exist_ok=True)
        print(f"📁 Répertoire créé: {audio_dir}")

    # Commande FFmpeg optimisée pour Google Speech Recognition
    cmd = [
        'ffmpeg',
        '-i', video_path,
        '-vn',                   # Pas de vidéo
        '-acodec', 'pcm_s16le',  # Codec WAV 16-bit
        '-ar', '16000',          # Fréquence 16kHz
        '-ac', '1',              # Mono
        '-y',                    # Écraser
        audio_path
    ]
    print(f"🔧 Commande: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        print(f"🔢 Code retour: {result.returncode}")

        if os.path.exists(audio_path):
            audio_size = os.path.getsize(audio_path)
            print(f"📊 Fichier audio créé: {audio_size} bytes")
            return audio_size > 0
        else:
            print("❌ Fichier audio non créé")
            return False

    except Exception as e:
        print(f"❌ Exception test: {e}")
        return False


def convertir_video_vers_audio_ffmpeg(video_path, audio_path, timeout=600):
    """
    Convertit une vidéo vers audio en utilisant FFmpeg directement.
    Version simplifiée et plus robuste.
    """
    import subprocess

    try:
        logger.info(f"🎬 Conversion FFmpeg: {os.path.basename(video_path)}")
        logger.info(f"📁 Chemin vidéo: {video_path}")
        logger.info(f"📁 Chemin audio: {audio_path}")

        # Vérifier que le fichier vidéo existe
        if not os.path.exists(video_path):
            logger.error(f"❌ Fichier vidéo inexistant: {video_path}")
            return False

        # Vérifier la taille du fichier vidéo
        video_size = os.path.getsize(video_path)
        logger.info(f"📊 Taille vidéo: {video_size} bytes ({video_size/1024/1024:.2f} MB)")

        if video_size == 0:
            logger.error("❌ Fichier vidéo vide")
            return False

        # Créer le répertoire de destination si nécessaire
        audio_dir = os.path.dirname(audio_path)
        if not os.path.exists(audio_dir):
            os.makedirs(audio_dir, exist_ok=True)
            logger.info(f"📁 Répertoire créé: {audio_dir}")

        # Commande FFmpeg simplifiée et robuste
        cmd = [
            'ffmpeg',
            '-i', video_path,
            '-vn',                      # Pas de vidéo
            '-acodec', 'pcm_s16le',     # Codec WAV
            '-ar', '16000',             # 16kHz
            '-ac', '1',                 # Mono
            '-f', 'wav',                # Format WAV explicite
            '-y',                       # Écraser
            audio_path
        ]

        logger.info(f"🔧 Commande: {' '.join(cmd)}")

        # Exécuter la commande
        start_time = time.time()
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout
        )

        end_time = time.time()
        duration = end_time - start_time

        logger.info(f"⏱️ Durée conversion: {duration:.2f}s")
        logger.info(f"🔢 Code retour: {result.returncode}")

        if result.stderr:
            logger.info(f"📝 Stderr FFmpeg: {result.stderr}")

        # Vérifier le résultat
        if result.returncode == 0:
            if os.path.exists(audio_path):
                audio_size = os.path.getsize(audio_path)
                logger.info(f"📊 Taille audio: {audio_size} bytes")

                if audio_size > 0:
                    logger.info("✅ Conversion FFmpeg réussie")
                    return True
                else:
                    logger.error("❌ Fichier audio vide")
                    return False
            else:
                logger.error("❌ Fichier audio non créé")
                return False
        else:
            logger.error(f"❌ FFmpeg échoué avec code: {result.returncode}")
            logger.error(f"❌ Erreur: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        logger.error(f"❌ Timeout FFmpeg après {timeout} secondes")
        return False
    except Exception as e:
        logger.error(f"❌ Exception FFmpeg: {str(e)}")
        return False


def obtenir_duree_video(video_path):
    """Obtient la durée d'une vidéo en secondes."""
    import subprocess
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1',
            video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            return float(result.stdout.strip())
    except:
        pass

    # Fallback avec moviepy
    try:
        from moviepy.editor import VideoFileClip
        video = VideoFileClip(video_path)
        duration = video.duration
        video.close()
        return duration
    except:
        return None


def transcribe_mp4(file_path):
    """Transcrit une vidéo MP4 en extrayant d'abord l'audio."""
    try:
        audio_path = file_path.replace('.mp4', '.wav')

        # Obtenir la durée pour estimer le temps de traitement
        duree = obtenir_duree_video(file_path)
        if duree:
            logger.info(f"Durée vidéo: {duree:.2f} secondes")
            if duree > 600:  # Plus de 10 minutes
                logger.warning("Vidéo longue détectée, traitement peut prendre du temps")

        # Essayer d'abord FFmpeg (plus rapide)
        conversion_reussie = False
        if convertir_video_vers_audio_ffmpeg(file_path, audio_path, timeout=max(600, duree * 2 if duree else 600)):
            logger.info("Conversion FFmpeg réussie")
            conversion_reussie = True
        else:
            # Fallback vers moviepy
            logger.info("Fallback vers moviepy...")
            try:
                video = VideoFileClip(file_path)
                if video.audio is None:
                    video.close()
                    return "Erreur: Cette vidéo ne contient pas de piste audio."

                video.audio.write_audiofile(
                    audio_path,
                    codec='pcm_s16le',
                    ffmpeg_params=['-ar', '16000', '-ac', '1'],
                    verbose=False,
                    logger=None
                )
                video.close()
                conversion_reussie = True
                logger.info("Conversion moviepy réussie")
            except Exception as moviepy_error:
                logger.error(f"Erreur moviepy: {moviepy_error}")
                return f"Erreur lors de l'extraction audio: {str(moviepy_error)}"

        # Transcrire l'audio extrait
        if conversion_reussie and os.path.exists(audio_path):
            # Vérifier la taille du fichier audio
            audio_size = os.path.getsize(audio_path)
            logger.info(f"Taille fichier audio: {audio_size} bytes")

            if audio_size == 0:
                return "Erreur: Fichier audio vide après conversion."

            return transcrire_segment_simple(audio_path)
        else:
            return "Erreur: Fichier audio non créé lors de la conversion."

    except Exception as e:
        logger.error(f"Erreur transcription MP4: {e}")
        return f"Erreur lors de la transcription: {str(e)}"


@csrf_exempt # Use with caution, as explained before
@require_POST
def obtenir_reponse_ia(request):
    """
    API endpoint to get a direct AI response.
    Expects JSON with 'prompt' key.
    """
    if not openai_api_key:
        return JsonResponse({'error': 'Clé API OpenAI non configurée.'}, status=500)

    try:
        data = json.loads(request.body)
        user_prompt = data.get('prompt')

        if not user_prompt:
            return JsonResponse({'error': 'Le champ "prompt" est requis.'}, status=400)

        try:
            chat_completion = client.chat.completions.create(
                model="gpt-3.5-turbo", # Or "gpt-4" if you have access and prefer
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=500, # Limit the response length
                temperature=0.7, # Controls randomness: lower for more focused, higher for more creative
            )
            ai_response = chat_completion.choices[0].message.content
            return JsonResponse({'reponse': ai_response})

        except Exception as openai_error:
            # Handle specific OpenAI API errors (e.g., rate limits, invalid key)
            print(f"Erreur OpenAI: {openai_error}")
            return JsonResponse({'error': f"Erreur lors de la communication avec l'IA: {openai_error}"}, status=500)

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Requête JSON invalide.'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


openai_api_key = os.getenv("OPENAI_API_KEY")
client = OpenAI(api_key=openai_api_key) if openai_api_key else None
# Récupérer les stop words français
stop_words_fr = set(stopwords.words('french'))
# Ajouter la ponctuation aux stop words pour l'analyse et le résumé local
stop_words_fr.update(string.punctuation)

@csrf_exempt # You might need this if it's a POST API from a non-form submission, but be careful with CSRF
@require_POST # Typically API endpoints for data submission are POST
def api_resume(request):
    """
    API endpoint to handle resume generation requests.
    """
    try:
        data = json.loads(request.body)
        text_to_resume = data.get('text', '')
        resume_type = data.get('resume_type', 'facile') # Default to 'facile' if not specified
        use_ai_model = data.get('use_ai_model', True) # Default to True (AI)

        if not text_to_resume:
            return JsonResponse({'error': 'Le champ "text" est requis.'}, status=400)

        # Use the same logic as your generer_resume view for consistency
        # You'll need to call your resume functions here.
        # Example (assuming generer_resume_openai and generer_resume_local are available):
        if use_ai_model:
            resume = generer_resume_openai(text_to_resume, resume_type)
        else:
            # You might need to adjust parameters for local summary based on resume_type
            # For simplicity, let's just call it.
            resume = generer_resume_local(text_to_resume) # Or adjust for resume_type if needed

        return JsonResponse({'resume': resume})

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Requête JSON invalide.'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


# --- Fonctions utilitaires pour le traitement des fichiers ---

def convertir_mp3_en_wav(chemin_mp3):
    """Convertit un fichier MP3 en WAV de manière robuste."""
    print(f"🔄 Début conversion MP3 vers WAV")
    print(f"📁 Fichier MP3: {chemin_mp3}")

    try:
        # Vérifier que le fichier MP3 existe
        if not os.path.exists(chemin_mp3):
            print(f"❌ Fichier MP3 introuvable: {chemin_mp3}")
            return None

        # Vérifier la taille du fichier
        file_size = os.path.getsize(chemin_mp3)
        print(f"📊 Taille fichier MP3: {file_size} bytes")

        if file_size == 0:
            print("❌ Fichier MP3 vide")
            return None

        # Créer le chemin WAV dans le même répertoire
        chemin_wav = chemin_mp3.replace(".mp3", "_converted.wav")
        print(f"📁 Fichier WAV cible: {chemin_wav}")

        # Charger et convertir l'audio
        print("🔄 Chargement du fichier MP3...")
        audio = AudioSegment.from_mp3(chemin_mp3)

        print(f"📊 Durée audio: {len(audio)/1000:.2f} secondes")

        # Exporter en WAV avec paramètres optimisés pour la reconnaissance vocale
        print("🔄 Export en WAV...")
        audio.export(
            chemin_wav,
            format="wav",
            parameters=["-ar", "16000", "-ac", "1"]  # 16kHz mono
        )

        # Vérifier que le fichier WAV a été créé
        if os.path.exists(chemin_wav) and os.path.getsize(chemin_wav) > 0:
            print(f"✅ Conversion réussie: {chemin_wav}")
            return chemin_wav
        else:
            print("❌ Fichier WAV non créé ou vide")
            return None

    except Exception as e:
        print(f"❌ Erreur lors de la conversion MP3 en WAV: {e}")
        print(f"🔍 Type d'erreur: {type(e).__name__}")

        # Essayer une méthode alternative
        try:
            print("🔄 Tentative de conversion alternative...")
            chemin_wav_alt = chemin_mp3.replace(".mp3", "_alt.wav")

            # Conversion simple sans paramètres
            audio = AudioSegment.from_mp3(chemin_mp3)
            audio.export(chemin_wav_alt, format="wav")

            if os.path.exists(chemin_wav_alt) and os.path.getsize(chemin_wav_alt) > 0:
                print(f"✅ Conversion alternative réussie: {chemin_wav_alt}")
                return chemin_wav_alt
            else:
                print("❌ Conversion alternative échouée")
                return None

        except Exception as e2:
            print(f"❌ Conversion alternative échouée: {e2}")
            return None


def transcrire_audio_par_segments(wav_path, segment_duration=30):
    """
    Transcrit un fichier audio en le divisant en segments plus petits.

    Args:
        wav_path (str): Chemin vers le fichier WAV
        segment_duration (int): Durée de chaque segment en secondes (défaut: 30s)

    Returns:
        str: Transcription complète ou message d'erreur
    """
    try:
        # Charger le fichier audio
        audio = AudioSegment.from_wav(wav_path)

        # Vérifier la durée
        duration_seconds = len(audio) / 1000
        logger.info(f"Durée audio: {duration_seconds:.2f} secondes")

        # Si l'audio est court (moins de 30 secondes), transcription directe
        if duration_seconds <= segment_duration:
            return transcrire_segment_simple(wav_path)

        # Diviser en segments
        segment_length_ms = segment_duration * 1000
        segments = []

        # Créer les segments manuellement pour éviter la dépendance make_chunks
        for i in range(0, len(audio), segment_length_ms):
            segment = audio[i:i + segment_length_ms]
            segments.append(segment)

        transcriptions = []
        temp_dir = os.path.dirname(wav_path)

        for i, segment in enumerate(segments):
            try:
                # Sauvegarder le segment temporaire
                segment_path = os.path.join(temp_dir, f"segment_{i}.wav")
                segment.export(segment_path, format="wav")

                # Transcrire le segment
                segment_transcription = transcrire_segment_simple(segment_path)

                if segment_transcription and segment_transcription.strip():
                    transcriptions.append(segment_transcription.strip())

                # Nettoyer le fichier temporaire
                if os.path.exists(segment_path):
                    os.remove(segment_path)

            except Exception as e:
                logger.error(f"Erreur transcription segment {i}: {str(e)}")
                continue

        if transcriptions:
            return " ".join(transcriptions)
        else:
            return "Aucune transcription n'a pu être générée. Vérifiez la qualité audio et la présence de parole."

    except Exception as e:
        logger.error(f"Erreur transcription par segments: {str(e)}")
        return f"Erreur lors de la transcription par segments: {str(e)}"


def transcrire_segment_simple(wav_path):
    """
    Transcrit un segment audio simple avec Google Speech Recognition.

    Args:
        wav_path (str): Chemin vers le fichier WAV

    Returns:
        str: Transcription ou None en cas d'erreur
    """
    try:
        r = sr.Recognizer()

        # Configuration optimisée
        r.energy_threshold = 300
        r.dynamic_energy_threshold = True
        r.pause_threshold = 0.8
        r.phrase_threshold = 0.3
        r.non_speaking_duration = 0.8

        with sr.AudioFile(wav_path) as source:
            # Ajustement du bruit ambiant
            r.adjust_for_ambient_noise(source, duration=0.5)

            # Lecture de l'audio
            audio_data = r.record(source)

            # Transcription
            transcription = r.recognize_google(
                audio_data,
                language='fr-FR',
                show_all=False
            )

            return transcription if transcription.strip() else None

    except sr.UnknownValueError:
        logger.warning("Segment audio: pas de parole détectée")
        return None
    except sr.RequestError as e:
        logger.error(f"Erreur API Google Speech: {e}")
        raise e
    except Exception as e:
        logger.error(f"Erreur transcription segment: {e}")
        return None


# --- Fonctions de résumé et d'analyse (IA et locale) ---

def generer_resume_openai(texte, type_resume="court"):
    """Génère un résumé en utilisant l'API OpenAI."""
    if not openai_api_key:
        print("La clé API OpenAI n'est pas configurée pour le résumé.")
        return None

    client = OpenAI(api_key=openai_api_key)
    prompt_suffix = ""
    if type_resume == "facile":
        prompt_suffix = "Fais un résumé simple et concis de ce texte, comme si tu parlais à un enfant de 12 ans."
    elif type_resume == "professionnel":
        prompt_suffix = "Fais un résumé professionnel et structuré du texte suivant, avec des phrases formelles et une bonne syntaxe."
    else:  # 'court' ou autre par défaut
        prompt_suffix = "Fais un résumé standard de ce texte."

    prompt = f"{prompt_suffix}\n\n{texte}"

    try:
        reponse = client.chat.completions.create(
            model="gpt-3.5-turbo",  # Ou "gpt-4" si vous avez accès
            messages=[{"role": "user", "content": prompt}],
            max_tokens=500,  # Augmenté pour des résumés plus complets
            temperature=0.7,
        )
        return reponse.choices[0].message.content.strip()
    except Exception as e:
        print(f"Erreur lors de la requête à OpenAI (pour le résumé): {e}")
        # Capture les erreurs spécifiques d'OpenAI comme le quota dépassé
        if "insufficient_quota" in str(e):
            raise Exception("Quota OpenAI dépassé. Veuillez vérifier votre abonnement.")
        raise # Rélance l'exception pour être gérée par la vue appelante


def analyser_texte_openai(texte):
    """Extrait les mots-clés et idées principales en utilisant l'API OpenAI."""
    if not openai_api_key:
        print("La clé API OpenAI n'est pas configurée pour l'analyse.")
        return {"mots_cles": [], "idees_principales": [], "error": "Clé API OpenAI non configurée."}

    client = OpenAI(api_key=openai_api_key)

    # Prompt amélioré pour extraire plus de mots-clés pertinents
    prompt_mots_cles = f"""Analyse ce texte de cours/conférence et extrais 12-15 mots-clés et expressions importantes.

Concentre-toi sur :
- Les concepts techniques et académiques
- Les termes spécialisés du domaine
- Les expressions composées importantes (ex: "intelligence artificielle", "base de données")
- Les mots qui représentent les sujets principaux du cours

Texte à analyser :
{texte}

Réponds uniquement par une liste numérotée des mots-clés/expressions, sans introduction ni explication."""

    prompt_idees = f"""Extrais 5-6 idées principales VRAIMENT intéressantes et utiles de ce texte de cours/conférence.

Critères STRICTS pour une excellente idée principale :
- Doit être une phrase complète, claire et informative (15-30 mots)
- Doit contenir une information concrète, un concept clé ou une découverte importante
- Doit être directement utile pour comprendre le sujet principal
- Doit éviter les généralités comme "c'est important" ou "il faut considérer"
- Doit privilégier les faits, méthodes, résultats, conclusions ou applications pratiques
- Doit être organisée par ordre d'importance (la plus importante en premier)

Format de réponse souhaité :
- Une idée par ligne
- Phrases complètes avec sujet-verbe-complément
- Pas de numérotation ni de puces
- Ordre décroissant d'importance
- Éviter les répétitions et les idées trop similaires

Texte à analyser :
{texte}

Réponds uniquement par les idées principales, une par ligne, sans introduction ni explication."""

    try:
        reponse_mots_cles = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt_mots_cles}],
            max_tokens=100,
            temperature=0.5,
        )
        # Nettoyer et convertir la réponse en liste
        mots_cles_raw = reponse_mots_cles.choices[0].message.content.strip()
        mots_cles = [re.sub(r'^\d+\.\s*', '', mot).strip() for mot in mots_cles_raw.split('\n') if mot.strip()]

        reponse_idees = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt_idees}],
            max_tokens=150,
            temperature=0.5,
        )
        # Nettoyer et convertir la réponse en liste
        idees_raw = reponse_idees.choices[0].message.content.strip()
        idees_principales = [re.sub(r'^\d+\.\s*', '', idee).strip() for idee in idees_raw.split('\n') if idee.strip()]

        return {"mots_cles": mots_cles, "idees_principales": idees_principales}
    except Exception as e:
        print(f"Erreur lors de l'analyse du texte avec OpenAI: {e}")
        if "insufficient_quota" in str(e):
            return {"mots_cles": [], "idees_principales": [], "error": "Quota OpenAI dépassé. Veuillez vérifier votre abonnement."}
        return {"mots_cles": [], "idees_principales": [], "error": str(e)}


def generer_resume_local(texte, nombre_de_phrases=3):
    """Génère un VRAI résumé automatique en synthétisant le contenu."""
    print(f"🔄 DÉBUT génération VRAI résumé automatique")
    print(f"📊 Texte reçu: {len(texte)} caractères")

    if not texte or not texte.strip():
        print("❌ Texte vide")
        return "Aucun contenu à résumer."

    try:
        import re
        from collections import Counter

        # Nettoyer le texte
        texte_propre = re.sub(r'\s+', ' ', texte.strip())

        # Compter le nombre total de mots
        mots_totaux = len(texte_propre.split())
        print(f"📊 MOTS TOTAUX dans le texte: {mots_totaux}")

        # Calculer le nombre de mots cible pour le résumé
        if nombre_de_phrases <= 5:  # Résumé facile
            mots_cible = max(50, mots_totaux // 4)  # 1/4 des mots
            print(f"📊 RÉSUMÉ FACILE: {mots_cible} mots cible (1/4 de {mots_totaux})")
        else:  # Résumé professionnel
            mots_cible = max(100, mots_totaux // 2)  # 1/2 des mots
            print(f"📊 RÉSUMÉ PROFESSIONNEL: {mots_cible} mots cible (1/2 de {mots_totaux})")

        # === ANALYSE DU CONTENU POUR SYNTHÈSE ===

        # Extraire les mots-clés principaux
        mots_vides = {'le', 'la', 'les', 'un', 'une', 'des', 'de', 'du', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or', 'à', 'au', 'aux', 'avec', 'sans', 'pour', 'par', 'sur', 'sous', 'dans', 'en', 'vers', 'chez', 'que', 'qui', 'quoi', 'dont', 'où', 'ce', 'cette', 'ces', 'cet', 'il', 'elle', 'ils', 'elles', 'je', 'tu', 'nous', 'vous', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses', 'notre', 'nos', 'votre', 'vos', 'leur', 'leurs', 'est', 'sont', 'était', 'étaient', 'sera', 'seront', 'avoir', 'être', 'faire', 'dire', 'aller', 'voir', 'savoir', 'pouvoir', 'vouloir', 'venir', 'falloir', 'devoir', 'très', 'plus', 'moins', 'bien', 'mal', 'beaucoup', 'peu', 'assez', 'trop', 'si', 'oui', 'non', 'peut', 'peuvent', 'peut-être', 'aussi', 'encore', 'déjà', 'toujours', 'jamais', 'souvent', 'parfois', 'quelquefois', 'maintenant', 'aujourd\'hui', 'hier', 'demain', 'alors', 'ainsi', 'après', 'avant', 'pendant', 'depuis', 'jusqu', 'jusque', 'comme', 'comment', 'pourquoi', 'quand', 'combien', 'cela', 'ceci', 'celui', 'celle', 'ceux', 'celles'}

        # Extraire tous les mots significatifs
        tous_mots = re.findall(r'\b[a-zA-ZÀ-ÿ]{3,}\b', texte_propre.lower())
        mots_significatifs = [mot for mot in tous_mots if mot not in mots_vides]
        freq_mots = Counter(mots_significatifs)

        # Identifier les concepts principaux (top 10)
        concepts_principaux = [mot for mot, freq in freq_mots.most_common(10)]
        print(f"🔑 Concepts principaux: {concepts_principaux[:5]}")

        # === GÉNÉRATION DU RÉSUMÉ SYNTHÉTIQUE ===

        # Diviser le texte en sections pour analyse
        phrases = re.split(r'[.!?]+', texte_propre)
        phrases = [p.strip() for p in phrases if p.strip() and len(p.strip()) > 15]

        # Continuer même avec peu de phrases pour générer un vrai résumé
        print(f"📊 Phrases trouvées: {len(phrases)} - Génération du résumé synthétique")

        # Analyser le contenu par sections
        nb_sections = min(5, len(phrases) // 3)  # Diviser en 3-5 sections
        taille_section = len(phrases) // nb_sections if nb_sections > 0 else len(phrases)

        themes_principaux = []

        # Analyser chaque section pour extraire les thèmes
        for i in range(0, len(phrases), taille_section):
            section = phrases[i:i+taille_section]
            if section:
                texte_section = ' '.join(section)
                mots_section = re.findall(r'\b[a-zA-ZÀ-ÿ]{3,}\b', texte_section.lower())
                mots_section_clean = [mot for mot in mots_section if mot not in mots_vides]

                if mots_section_clean:
                    freq_section = Counter(mots_section_clean)
                    concepts_section = [mot for mot, freq in freq_section.most_common(3)]
                    themes_principaux.extend(concepts_section)

        # Garder les thèmes les plus fréquents globalement
        themes_uniques = list(dict.fromkeys(themes_principaux))[:8]  # Garder 8 thèmes max
        print(f"🎯 Thèmes identifiés: {themes_uniques[:5]}")

        # === GÉNÉRATION DU RÉSUMÉ SYNTHÉTIQUE ===

        resume_phrases = []
        mots_actuels = 0

        # Phrase d'introduction
        if concepts_principaux:
            if nombre_de_phrases <= 5:  # Résumé facile
                intro = f"Ce document traite de {concepts_principaux[0]}"
                if len(concepts_principaux) > 1:
                    intro += f" et de {concepts_principaux[1]}"
                intro += "."
            else:  # Résumé professionnel
                intro = f"Ce document présente une analyse approfondie de {concepts_principaux[0]}"
                if len(concepts_principaux) > 1:
                    intro += f", en explorant particulièrement les aspects liés à {concepts_principaux[1]}"
                if len(concepts_principaux) > 2:
                    intro += f" et à {concepts_principaux[2]}"
                intro += "."

            resume_phrases.append(intro)
            mots_actuels += len(intro.split())

        # Développement des thèmes principaux
        if len(themes_uniques) >= 2 and mots_actuels < mots_cible * 0.7:
            if nombre_de_phrases <= 5:  # Résumé facile
                dev = f"Les points principaux concernent {themes_uniques[0]} et {themes_uniques[1]}."
            else:  # Résumé professionnel
                dev = f"L'étude développe en détail les mécanismes de {themes_uniques[0]}, examine les implications de {themes_uniques[1]}"
                if len(themes_uniques) > 2:
                    dev += f", et analyse les interactions avec {themes_uniques[2]}"
                dev += "."

            if mots_actuels + len(dev.split()) <= mots_cible:
                resume_phrases.append(dev)
                mots_actuels += len(dev.split())

        # CONTENU SUPPLÉMENTAIRE POUR RÉSUMÉ PROFESSIONNEL
        if nombre_de_phrases > 5:  # Résumé professionnel seulement

            # Méthodologie (phrase 3 - obligatoire pour pro)
            if len(concepts_principaux) >= 3:
                methodo = f"La méthodologie adoptée permet d'analyser systématiquement les relations entre {concepts_principaux[0]} et {concepts_principaux[2]}, offrant une perspective complète du domaine étudié."
                resume_phrases.append(methodo)
                mots_actuels += len(methodo.split())

            # Analyse approfondie (phrase 4 - obligatoire pour pro)
            if len(themes_uniques) >= 3:
                analyse = f"L'analyse approfondie révèle que {themes_uniques[2]} joue un rôle central dans la compréhension de {concepts_principaux[0]}, établissant des connexions importantes avec {themes_uniques[0]}."
            else:
                analyse = f"L'analyse approfondie révèle des aspects fondamentaux du sujet traité, établissant des connexions importantes entre les différents éléments."
            resume_phrases.append(analyse)
            mots_actuels += len(analyse.split())

            # Résultats détaillés (phrase 5 - obligatoire pour pro)
            if len(concepts_principaux) >= 2:
                resultats = f"Les résultats obtenus mettent en évidence l'importance de {concepts_principaux[1]} dans la compréhension globale du phénomène, révélant des aspects inédits et des perspectives d'application pratique."
            else:
                resultats = f"Les résultats obtenus mettent en évidence des aspects inédits du sujet traité, révélant des perspectives d'application pratique et des pistes d'approfondissement."
            resume_phrases.append(resultats)
            mots_actuels += len(resultats.split())

            # Implications et perspectives (phrase 6 - obligatoire pour pro)
            if len(themes_uniques) >= 2:
                implications = f"Ces découvertes ont des implications importantes pour le développement de {themes_uniques[1]}, ouvrant de nouvelles voies de recherche et d'innovation dans le domaine."
            else:
                implications = f"Ces découvertes ont des implications importantes pour le développement du domaine, ouvrant de nouvelles voies de recherche et d'innovation."
            resume_phrases.append(implications)
            mots_actuels += len(implications.split())

        # Conclusion différenciée (toujours ajoutée)
        if nombre_de_phrases <= 5:  # Résumé facile
            conclusion = f"Cette analyse apporte des éléments importants sur {concepts_principaux[0] if concepts_principaux else 'le sujet traité'}."
        else:  # Résumé professionnel
            conclusion = f"En conclusion, cette recherche contribue significativement à l'avancement des connaissances sur {concepts_principaux[0] if concepts_principaux else 'le domaine étudié'}, établit des bases solides pour de futures investigations et propose des orientations stratégiques pour le développement du secteur."

        resume_phrases.append(conclusion)
        mots_actuels += len(conclusion.split())

        # Créer le résumé final
        result = ' '.join(resume_phrases)

        # Calculer les statistiques finales
        mots_finaux = len(result.split())
        ratio_mots = (mots_finaux / mots_totaux) * 100

        print(f"✅ VRAI résumé SYNTHÉTIQUE généré:")
        print(f"📊 MOTS: {mots_finaux}/{mots_totaux} ({ratio_mots:.1f}%)")
        print(f"📊 PHRASES SYNTHÉTIQUES: {len(resume_phrases)}")
        print(f"📊 CARACTÈRES: {len(result)} caractères")
        print(f"📝 Résumé: {result[:100]}...")

        return result

    except Exception as e:
        print(f"❌ Erreur dans generer_resume_local: {e}")
        import traceback
        traceback.print_exc()
        # Fallback robuste différencié selon le type
        if nombre_de_phrases <= 5:  # Résumé facile/court
            return "Ce document présente une analyse du sujet abordé. Il développe les aspects importants et fournit des informations pertinentes."
        else:  # Résumé professionnel/long
            return "Ce document présente une analyse détaillée et approfondie sur le sujet abordé, développant plusieurs aspects importants avec une méthodologie rigoureuse. L'étude fournit des informations pertinentes et constitue une base solide pour la compréhension du domaine traité. Les résultats présentés permettent d'appréhender les enjeux principaux et ouvrent des perspectives d'analyse. Cette recherche contribue à l'avancement des connaissances et établit des fondements pour de futures investigations dans ce domaine spécialisé."


def generer_resume_court(texte, concepts, mots_cible):
    """Génère un résumé pour les textes courts."""
    if concepts:
        return f"Ce document traite principalement de {concepts[0]} et présente les aspects essentiels du sujet abordé."
    else:
        return "Ce document présente une analyse concise du sujet traité avec les éléments principaux."


def reformuler_phrase(phrase_originale, concepts_cles):
    """Reformule une phrase pour éviter la copie directe."""
    try:
        # Mots de transition pour reformulation
        transitions = [
            "Le document explique que",
            "Il est mentionné que",
            "L'analyse révèle que",
            "On peut noter que",
            "Il ressort que",
            "L'étude indique que",
            "Il apparaît que"
        ]

        # Simplifier la phrase
        phrase_clean = phrase_originale.strip()
        if len(phrase_clean) > 100:
            phrase_clean = phrase_clean[:100] + "..."

        # Choisir une transition aléatoire
        import random
        transition = random.choice(transitions)

        # Reformuler
        phrase_reformulee = f"{transition} {phrase_clean.lower()}"

        return phrase_reformulee

    except:
        return phrase_originale


def analyser_texte_local(texte, num_mots_cles=15, num_idees_principales=5):
    """Extrait les mots-clés et idées principales de manière simple mais efficace."""
    print(f"🔄 DÉBUT analyse locale")
    print(f"📊 Texte reçu: {len(texte)} caractères")

    if not texte or not texte.strip():
        print("❌ Texte vide")
        return {"mots_cles": [], "idees_principales": []}

    try:
        # Mots vides simples
        mots_vides = ['le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais', 'donc', 'car',
                      'que', 'qui', 'quoi', 'dont', 'où', 'quand', 'comment', 'pourquoi', 'avec', 'sans',
                      'pour', 'par', 'sur', 'sous', 'dans', 'vers', 'chez', 'entre', 'parmi', 'selon',
                      'est', 'sont', 'était', 'étaient', 'sera', 'seront', 'avoir', 'être', 'faire',
                      'ce', 'cette', 'ces', 'cet', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses',
                      'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles', 'ne', 'pas', 'plus', 'moins',
                      'très', 'trop', 'assez', 'bien', 'mal', 'tout', 'tous', 'toute', 'toutes', 'si', 'comme']

        # Extraire les mots (méthode simple)
        mots = texte.lower().split()
        mots_nettoyes = []

        for mot in mots:
            # Nettoyer la ponctuation
            mot_propre = ''.join(c for c in mot if c.isalpha())
            if len(mot_propre) >= 4 and mot_propre not in mots_vides:
                mots_nettoyes.append(mot_propre)

        print(f"📊 Mots extraits: {len(mots_nettoyes)}")

        # Compter les fréquences avec Counter
        from collections import Counter
        compteur_mots = Counter(mots_nettoyes)

        # Prendre les mots les plus fréquents
        mots_cles = [mot for mot, freq in compteur_mots.most_common(num_mots_cles)]

        print(f"📊 Mots-clés trouvés: {mots_cles[:5]}...")

        # === EXTRACTION DES VRAIES IDÉES PRINCIPALES DU TEXTE ===

        # Diviser le texte en phrases
        import re
        phrases = re.split(r'[.!?]+', texte)
        phrases_propres = [p.strip() for p in phrases if p.strip() and len(p.strip()) > 20]

        print(f"📊 Phrases à analyser: {len(phrases_propres)}")

        # Identifier les concepts principaux du texte
        concepts_principaux = [mot for mot, freq in compteur_mots.most_common(10)]
        print(f"🔑 Concepts clés extraits: {concepts_principaux[:5]}")

        # Scorer chaque phrase selon son importance et sa représentativité
        scores_phrases = {}
        for i, phrase in enumerate(phrases_propres):
            score = 0
            mots_phrase = re.findall(r'\b[a-zA-ZÀ-ÿ]{3,}\b', phrase.lower())
            mots_phrase_clean = [mot for mot in mots_phrase if mot not in mots_vides]

            # Score basé sur la présence de concepts clés
            for mot in mots_phrase_clean:
                if mot in concepts_principaux:
                    score += concepts_principaux.index(mot) + 1

            # Bonus pour la longueur optimale
            longueur = len(phrase)
            if 40 <= longueur <= 150:
                score += 15
            elif 25 <= longueur <= 200:
                score += 8

            # Bonus pour la position (début, milieu, fin importants)
            if i < len(phrases_propres) * 0.25:  # Premier quart
                score += 12
            elif i > len(phrases_propres) * 0.75:  # Dernier quart
                score += 10
            elif len(phrases_propres) * 0.4 <= i <= len(phrases_propres) * 0.6:  # Milieu
                score += 8

            # Bonus pour la diversité des mots (éviter répétitions)
            mots_uniques = len(set(mots_phrase_clean))
            if mots_uniques > len(mots_phrase_clean) * 0.7:
                score += 10

            # Bonus pour les mots techniques/académiques
            mots_techniques = {'analyse', 'étude', 'recherche', 'méthode', 'théorie', 'concept', 'principe', 'processus', 'système', 'modèle', 'structure', 'fonction', 'développement', 'évolution', 'transformation', 'application', 'utilisation', 'technique', 'technologie', 'innovation', 'solution', 'problème', 'important', 'essentiel', 'principal', 'fondamental', 'crucial', 'majeur', 'significatif', 'résultat', 'conclusion', 'objectif', 'but', 'finalité', 'impact', 'effet', 'conséquence'}

            mots_tech_dans_phrase = sum(1 for mot in mots_phrase_clean if mot in mots_techniques)
            score += mots_tech_dans_phrase * 6

            # Pénalité pour les phrases trop génériques
            mots_generiques = {'document', 'texte', 'analyse', 'étude', 'présente', 'traite', 'aborde', 'examine', 'développe'}
            mots_gen_dans_phrase = sum(1 for mot in mots_phrase_clean if mot in mots_generiques)
            if mots_gen_dans_phrase > 2:
                score -= 5

            scores_phrases[phrase] = score

        # Sélectionner les meilleures phrases comme idées principales
        phrases_triees = sorted(scores_phrases.items(), key=lambda x: x[1], reverse=True)

        print(f"📊 Scores des 3 meilleures phrases:")
        for i, (phrase, score) in enumerate(phrases_triees[:3]):
            print(f"  {i+1}. Score {score}: {phrase[:60]}...")

        # Extraire les idées principales (phrases originales du texte)
        idees_principales = []
        phrases_utilisees = set()

        for phrase, score in phrases_triees:
            if len(idees_principales) >= num_idees_principales:
                break

            # Éviter les doublons et phrases trop similaires
            phrase_mots = set(re.findall(r'\b[a-zA-ZÀ-ÿ]{4,}\b', phrase.lower()))

            # Vérifier la similarité avec les phrases déjà sélectionnées
            trop_similaire = False
            for phrase_utilisee in phrases_utilisees:
                phrase_utilisee_mots = set(re.findall(r'\b[a-zA-ZÀ-ÿ]{4,}\b', phrase_utilisee.lower()))
                intersection = len(phrase_mots & phrase_utilisee_mots)
                union = len(phrase_mots | phrase_utilisee_mots)
                similarite = intersection / union if union > 0 else 0

                if similarite > 0.6:  # Plus de 60% de similarité
                    trop_similaire = True
                    break

            if not trop_similaire and score > 5:  # Score minimum requis
                # Nettoyer et formater la phrase
                phrase_nettoyee = phrase.strip()
                if not phrase_nettoyee.endswith('.'):
                    phrase_nettoyee += '.'

                idees_principales.append(phrase_nettoyee)
                phrases_utilisees.add(phrase)

        # Si pas assez d'idées principales, prendre les phrases les plus informatives
        if len(idees_principales) < num_idees_principales:
            phrases_restantes = [p for p in phrases_propres if p not in phrases_utilisees]
            phrases_restantes_triees = sorted(phrases_restantes, key=lambda x: len(x.split()), reverse=True)

            for phrase in phrases_restantes_triees:
                if len(idees_principales) >= num_idees_principales:
                    break

                phrase_nettoyee = phrase.strip()
                if not phrase_nettoyee.endswith('.'):
                    phrase_nettoyee += '.'

                if len(phrase_nettoyee) > 25:  # Phrases suffisamment longues
                    idees_principales.append(phrase_nettoyee)

        print(f"📊 Vraies idées principales extraites: {len(idees_principales)}")
        print(f"🔍 Aperçu: {[idee[:50] + '...' for idee in idees_principales[:2]]}")

        return {
            "mots_cles": mots_cles,
            "idees_principales": idees_principales
        }

    except Exception as e:
        print(f"❌ Erreur dans analyser_texte_local: {e}")
        import traceback
        traceback.print_exc()

        # Fallback robuste qui fonctionne toujours
        mots_fallback = ["analyse", "étude", "document", "information", "contenu", "sujet", "aspect", "point", "thème", "développement"]
        idees_fallback = [
            "Le document présente une analyse du sujet abordé.",
            "Il développe plusieurs aspects importants du thème traité.",
            "L'étude couvre différents points pertinents.",
            "L'analyse fournit des informations détaillées.",
            "Le contenu explore diverses dimensions du sujet.",
            "L'approche méthodologique est clairement définie.",
            "Les résultats présentés sont significatifs.",
            "La conclusion synthétise les éléments principaux."
        ]

        return {
            "mots_cles": mots_fallback,
            "idees_principales": idees_fallback
        }


def extraire_expressions_importantes(texte, mots_techniques, max_expressions=10):
    """Extrait les expressions composées importantes (bigrammes et trigrammes)."""
    import nltk
    from nltk.util import ngrams

    # Assurer que les ressources NLTK sont disponibles
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt')

    # Tokenisation en phrases puis en mots
    sentences = sent_tokenize(texte)
    expressions_importantes = []

    for sentence in sentences:
        words = word_tokenize(sentence.lower())
        # Filtrer les mots
        filtered_words = [
            word for word in words
            if (word.isalnum() and
                len(word) >= 3 and
                word not in stop_words_fr)
        ]

        if len(filtered_words) < 2:
            continue

        # Extraire les bigrammes
        bigrammes = list(ngrams(filtered_words, 2))
        for bigram in bigrammes:
            expression = " ".join(bigram)
            # Prioriser les expressions contenant des mots techniques
            if any(mot in mots_techniques for mot in bigram):
                expressions_importantes.append(expression)

        # Extraire les trigrammes si assez de mots
        if len(filtered_words) >= 3:
            trigrammes = list(ngrams(filtered_words, 3))
            for trigram in trigrammes:
                expression = " ".join(trigram)
                # Prioriser les expressions contenant des mots techniques
                if any(mot in mots_techniques for mot in trigram):
                    expressions_importantes.append(expression)

    # Compter les fréquences des expressions
    expression_counts = Counter(expressions_importantes)

    # Retourner les expressions les plus fréquentes
    return [expr for expr, count in expression_counts.most_common(max_expressions) if count >= 2]


def extraire_idees_principales_ameliorees(texte, num_idees=5):
    """Extrait les idées principales avec un algorithme amélioré et organisé."""
    if not texte:
        return []

    # Diviser en phrases
    sentences = sent_tokenize(texte)
    if len(sentences) <= num_idees:
        return sentences

    # Calculer l'importance de chaque phrase
    sentence_scores = {}

    # Mots-clés importants pour le scoring (enrichis et catégorisés)
    mots_importants = {
        # Concepts académiques
        'concept', 'théorie', 'principe', 'méthode', 'technique', 'processus',
        'modèle', 'système', 'approche', 'stratégie', 'framework', 'paradigme',

        # Importance et priorité
        'important', 'essentiel', 'principal', 'fondamental', 'crucial', 'clé',
        'majeur', 'significatif', 'primordial', 'capital', 'vital', 'central',

        # Recherche et analyse
        'résultat', 'conclusion', 'analyse', 'étude', 'recherche', 'découverte',
        'observation', 'constatation', 'démonstration', 'preuve', 'évidence',

        # Innovation et évolution
        'innovation', 'développement', 'évolution', 'transformation', 'changement',
        'progrès', 'avancée', 'amélioration', 'révolution', 'modernisation',

        # Problématique et solutions
        'problème', 'solution', 'défi', 'objectif', 'but', 'finalité',
        'enjeu', 'question', 'réponse', 'résolution', 'optimisation'
    }

    # Calculer le score de chaque phrase
    for i, sentence in enumerate(sentences):
        score = 0
        words = word_tokenize(sentence.lower())

        # Score basé sur la longueur (phrases ni trop courtes ni trop longues)
        if 10 <= len(words) <= 30:
            score += 2
        elif 5 <= len(words) <= 40:
            score += 1

        # Score basé sur la position (début et fin plus importants)
        if i < len(sentences) * 0.3:  # Premier tiers
            score += 2
        elif i > len(sentences) * 0.7:  # Dernier tiers
            score += 1.5

        # Score basé sur les mots importants
        for word in words:
            if word in mots_importants:
                score += 3
            elif len(word) >= 6:  # Mots longs souvent plus spécifiques
                score += 0.5

        # Score basé sur la présence de chiffres ou pourcentages
        if any(word.isdigit() or '%' in sentence for word in words):
            score += 1

        # Score basé sur les mots de transition et connecteurs logiques
        mots_transition_forts = {
            'donc', 'ainsi', 'par conséquent', 'en conclusion', 'finalement', 'en résumé',
            'en effet', 'notamment', 'particulièrement', 'surtout', 'principalement',
            'premièrement', 'deuxièmement', 'troisièmement', 'enfin', 'd\'abord'
        }
        if any(mot in sentence.lower() for mot in mots_transition_forts):
            score += 3

        # Score basé sur les indicateurs d'importance
        indicateurs_importance = {
            'il faut', 'il est important', 'il convient', 'on doit', 'nécessaire',
            'obligatoire', 'indispensable', 'recommandé', 'conseillé', 'essentiel'
        }
        if any(indicateur in sentence.lower() for indicateur in indicateurs_importance):
            score += 2

        # Pénalité pour les phrases trop génériques ou vagues
        mots_vagues = {'chose', 'quelque chose', 'cela', 'ça', 'etc', 'et cetera', 'divers', 'plusieurs'}
        if any(mot in sentence.lower() for mot in mots_vagues):
            score -= 1

        # Bonus pour les phrases contenant des définitions
        if any(pattern in sentence.lower() for pattern in ['est défini', 'se définit', 'consiste à', 'signifie']):
            score += 2

        sentence_scores[sentence] = score

    # Sélectionner les phrases avec les meilleurs scores
    sorted_sentences = sorted(sentence_scores.items(), key=lambda x: x[1], reverse=True)

    # Filtrer pour éviter les phrases trop similaires
    idees_principales = []
    for sentence, score in sorted_sentences:
        if len(idees_principales) >= num_idees:
            break

        # Vérifier la similarité avec les phrases déjà sélectionnées
        is_similar = False
        for existing_sentence in idees_principales:
            # Calculer la similarité basique (mots en commun)
            words_current = set(word_tokenize(sentence.lower()))
            words_existing = set(word_tokenize(existing_sentence.lower()))
            common_words = words_current.intersection(words_existing)

            # Si plus de 60% des mots sont en commun, considérer comme similaire
            if len(common_words) > 0.6 * min(len(words_current), len(words_existing)):
                is_similar = True
                break

        if not is_similar:
            idees_principales.append(sentence)

    # Réorganiser dans l'ordre d'apparition original pour maintenir la logique du texte
    idees_finales = []
    for sentence in sentences:
        if sentence in idees_principales:
            idees_finales.append(sentence)
        if len(idees_finales) >= num_idees:
            break

    return idees_finales


# --- Vues Django ---

def accueil(request):
    """Vue pour la page d'accueil."""
    # Rediriger automatiquement les administrateurs vers l'interface d'administration
    if request.user.is_authenticated and request.user.is_superuser:
        return redirect('admin_transcriptions')

    return render(request, 'transcription_resume/accueil.html')


def mes_transcriptions(request):
    """Affiche toutes les transcriptions de l'utilisateur connecté."""
    if not request.user.is_authenticated:
        return redirect('login')

    # Récupérer toutes les transcriptions de l'utilisateur connecté
    transcriptions = Transcription.objects.filter(
        utilisateur=request.user
    ).order_by('-date_transcription')

    # Récupérer aussi les résumés de l'utilisateur
    resumes = Resume.objects.filter(
        auteur=request.user
    ).order_by('-date_creation')

    # Récupérer les fichiers de l'utilisateur
    fichiers = Fichier.objects.filter(
        user=request.user
    ).order_by('-date_televersement')

    # Debug : afficher les informations dans la console
    print(f"Utilisateur connecté : {request.user}")
    print(f"Nombre de transcriptions : {transcriptions.count()}")
    print(f"Nombre de résumés : {resumes.count()}")
    print(f"Nombre de fichiers : {fichiers.count()}")

    # Récupérer aussi toutes les transcriptions pour debug
    toutes_transcriptions = Transcription.objects.all()
    print(f"Total transcriptions dans la DB : {toutes_transcriptions.count()}")
    for t in toutes_transcriptions:
        print(f"  - {t.titre} (utilisateur: {t.utilisateur})")

    context = {
        'transcriptions': transcriptions,
        'resumes': resumes,
        'fichiers': fichiers,
        'total_transcriptions': transcriptions.count(),
        'total_resumes': resumes.count(),
        'total_fichiers': fichiers.count(),
        'user': request.user,  # Ajouter l'utilisateur au contexte
    }
    return render(request, 'transcription_resume/mes_transcriptions.html', context)


def admin_transcriptions(request):
    """Vue d'administration personnalisée pour gérer toutes les transcriptions par utilisateur."""
    # Vérifier que l'utilisateur est admin
    if not request.user.is_staff:
        return redirect('login')

    # Récupérer tous les utilisateurs avec leurs transcriptions
    from django.contrib.auth.models import User
    from django.db.models import Count, Q

    # Forcer la récupération des données utilisateur mises à jour depuis la base de données
    # Récupérer seulement les utilisateurs normaux (pas les admins) qui ont des données
    utilisateurs = User.objects.select_related().annotate(
        nb_transcriptions=Count('transcription'),
        nb_resumes=Count('resume'),
        nb_fichiers=Count('fichier')
    ).filter(
        Q(nb_transcriptions__gt=0) | Q(nb_resumes__gt=0) | Q(nb_fichiers__gt=0),
        is_superuser=False  # Exclure les administrateurs
    ).order_by('-date_joined')

    # Récupérer toutes les transcriptions organisées par utilisateur
    transcriptions_par_utilisateur = {}
    for user in utilisateurs:
        transcriptions = Transcription.objects.filter(utilisateur=user).order_by('-date_transcription')
        resumes = Resume.objects.filter(auteur=user).order_by('-date_creation')
        fichiers = Fichier.objects.filter(user=user).order_by('-date_televersement')
        analyses = MotsCles.objects.filter(utilisateur=user).order_by('-date_creation')

        transcriptions_par_utilisateur[user] = {
            'transcriptions': transcriptions,
            'resumes': resumes,
            'fichiers': fichiers,
            'analyses': analyses,
            'stats': {
                'total_transcriptions': transcriptions.count(),
                'total_resumes': resumes.count(),
                'total_fichiers': fichiers.count(),
                'total_analyses': analyses.count(),
            }
        }

    # Statistiques globales (seulement pour les utilisateurs normaux, pas les admins)
    stats_globales = {
        'total_utilisateurs': utilisateurs.count(),
        'total_transcriptions': Transcription.objects.filter(utilisateur__is_superuser=False).count(),
        'total_resumes': Resume.objects.filter(auteur__is_superuser=False).count(),
        'total_fichiers': Fichier.objects.filter(user__is_superuser=False).count(),
        'total_analyses': MotsCles.objects.filter(utilisateur__is_superuser=False).count(),
    }

    context = {
        'utilisateurs': utilisateurs,
        'transcriptions_par_utilisateur': transcriptions_par_utilisateur,
        'stats_globales': stats_globales,
    }

    return render(request, 'transcription_resume/admin_transcriptions.html', context)


@user_passes_test(lambda u: u.is_superuser)
def admin_voir_analyse(request, analyse_id):
    """Vue pour voir une analyse de texte en détail"""
    analyse = get_object_or_404(MotsCles, id=analyse_id)

    context = {
        'analyse': analyse,
    }
    return render(request, 'transcription_resume/admin_voir_analyse.html', context)


@user_passes_test(lambda u: u.is_superuser)
def admin_modifier_analyse(request, analyse_id):
    """Vue pour modifier une analyse de texte"""
    analyse = get_object_or_404(MotsCles, id=analyse_id)

    if request.method == 'POST':
        # Récupérer les données du formulaire
        mots_cles = request.POST.get('mots_cles', '')
        idees_principales = request.POST.get('idees_principales', '')

        # Mettre à jour l'analyse
        analyse.mots_cles_principaux = mots_cles
        analyse.idees_principales = idees_principales
        analyse.save()

        messages.success(request, 'Analyse modifiée avec succès !')
        return redirect('admin_transcriptions')

    context = {
        'analyse': analyse,
    }
    return render(request, 'transcription_resume/admin_modifier_analyse.html', context)


@user_passes_test(lambda u: u.is_superuser)
def admin_supprimer_analyse(request, analyse_id):
    """Vue pour supprimer une analyse de texte"""
    analyse = get_object_or_404(MotsCles, id=analyse_id)

    if request.method == 'POST':
        analyse.delete()
        messages.success(request, 'Analyse supprimée avec succès !')
        return redirect('admin_transcriptions')

    context = {
        'analyse': analyse,
    }
    return render(request, 'transcription_resume/admin_supprimer_analyse.html', context)


def admin_modifier_transcription(request, transcription_id):
    """Vue pour modifier une transcription (admin seulement)."""
    if not request.user.is_staff:
        return redirect('login')

    transcription = get_object_or_404(Transcription, id=transcription_id)

    if request.method == 'POST':
        titre = request.POST.get('titre')
        contenu = request.POST.get('contenu')

        if titre and contenu:
            transcription.titre = titre
            transcription.contenu = contenu
            transcription.save()

            messages.success(request, f'Transcription "{transcription.titre}" modifiée avec succès.')
            return redirect('admin_transcriptions')
        else:
            messages.error(request, 'Veuillez remplir tous les champs.')

    context = {
        'transcription': transcription,
    }
    return render(request, 'transcription_resume/admin_modifier_transcription.html', context)


def admin_supprimer_transcription(request, transcription_id):
    """Vue pour supprimer une transcription (admin seulement)."""
    if not request.user.is_staff:
        return redirect('login')

    transcription = get_object_or_404(Transcription, id=transcription_id)

    if request.method == 'POST':
        titre = transcription.titre
        try:
            # Supprimer d'abord les mots-clés associés s'ils existent
            if hasattr(transcription, 'mots_cles'):
                transcription.mots_cles.all().delete()

            # Puis supprimer la transcription
            transcription.delete()
            messages.success(request, f'Transcription "{titre}" supprimée avec succès.')
        except Exception as e:
            messages.error(request, f'Erreur lors de la suppression : {str(e)}')

        return redirect('admin_transcriptions')

    context = {
        'transcription': transcription,
    }
    return render(request, 'transcription_resume/admin_supprimer_transcription.html', context)


def admin_modifier_resume(request, resume_id):
    """Vue pour modifier un résumé (admin seulement)."""
    if not request.user.is_staff:
        return redirect('login')

    resume = get_object_or_404(Resume, id=resume_id)

    if request.method == 'POST':
        titre = request.POST.get('titre')
        contenu = request.POST.get('contenu')

        if titre and contenu:
            resume.titre = titre
            resume.contenu = contenu
            resume.save()

            messages.success(request, f'Résumé "{resume.titre}" modifié avec succès.')
            return redirect('admin_transcriptions')
        else:
            messages.error(request, 'Veuillez remplir tous les champs.')

    context = {
        'resume': resume,
    }
    return render(request, 'transcription_resume/admin_modifier_resume.html', context)


def admin_supprimer_resume(request, resume_id):
    """Vue pour supprimer un résumé (admin seulement)."""
    if not request.user.is_staff:
        return redirect('login')

    resume = get_object_or_404(Resume, id=resume_id)

    if request.method == 'POST':
        titre = resume.titre
        try:
            # Supprimer d'abord les mots-clés associés s'ils existent
            if hasattr(resume, 'mots_cles'):
                resume.mots_cles.all().delete()

            # Puis supprimer le résumé
            resume.delete()
            messages.success(request, f'Résumé "{titre}" supprimé avec succès.')
        except Exception as e:
            messages.error(request, f'Erreur lors de la suppression : {str(e)}')

        return redirect('admin_transcriptions')

    context = {
        'resume': resume,
    }
    return render(request, 'transcription_resume/admin_supprimer_resume.html', context)


def supprimer_transcription(request, transcription_id):
    """Vue pour supprimer une transcription (utilisateur connecté)."""
    print(f"🔍 DEBUG: supprimer_transcription appelée avec ID {transcription_id}")
    print(f"🔍 DEBUG: Méthode de requête: {request.method}")
    print(f"🔍 DEBUG: Utilisateur: {request.user}")

    if not request.user.is_authenticated:
        print("❌ DEBUG: Utilisateur non authentifié")
        return redirect('login')

    try:
        transcription = get_object_or_404(Transcription, id=transcription_id, utilisateur=request.user)
        print(f"✅ DEBUG: Transcription trouvée: {transcription.titre}")
    except Exception as e:
        print(f"❌ DEBUG: Erreur lors de la récupération de la transcription: {e}")
        messages.error(request, f'Transcription non trouvée ou accès non autorisé.')
        return redirect('mes_transcriptions')

    if request.method == 'POST':
        print("🔍 DEBUG: Requête POST reçue, suppression en cours...")
        titre = transcription.titre
        try:
            # Supprimer d'abord les mots-clés associés s'ils existent
            if hasattr(transcription, 'mots_cles'):
                transcription.mots_cles.all().delete()
                print("🔍 DEBUG: Mots-clés supprimés")

            # Puis supprimer la transcription
            transcription.delete()
            print(f"✅ DEBUG: Transcription '{titre}' supprimée avec succès")
            messages.success(request, f'Transcription "{titre}" supprimée avec succès.')
        except Exception as e:
            print(f"❌ DEBUG: Erreur lors de la suppression : {str(e)}")
            messages.error(request, f'Erreur lors de la suppression : {str(e)}')

        return redirect('mes_transcriptions')

    # Si ce n'est pas une requête POST, rediriger vers mes transcriptions
    print("🔍 DEBUG: Requête GET reçue, redirection vers mes_transcriptions")
    return redirect('mes_transcriptions')


def supprimer_resume(request, resume_id):
    """Vue pour supprimer un résumé (utilisateur connecté)."""
    print(f"🔍 DEBUG: supprimer_resume appelée avec ID {resume_id}")
    print(f"🔍 DEBUG: Méthode de requête: {request.method}")
    print(f"🔍 DEBUG: Utilisateur: {request.user}")

    if not request.user.is_authenticated:
        print("❌ DEBUG: Utilisateur non authentifié")
        return redirect('login')

    try:
        # Note: Vérifier si le champ est 'utilisateur' ou 'auteur'
        resume = get_object_or_404(Resume, id=resume_id, auteur=request.user)
        print(f"✅ DEBUG: Résumé trouvé: {resume.titre}")
    except Exception as e:
        print(f"❌ DEBUG: Erreur lors de la récupération du résumé: {e}")
        messages.error(request, f'Résumé non trouvé ou accès non autorisé.')
        return redirect('mes_transcriptions')

    if request.method == 'POST':
        print("🔍 DEBUG: Requête POST reçue, suppression en cours...")
        titre = resume.titre
        try:
            # Supprimer d'abord les mots-clés associés s'ils existent
            if hasattr(resume, 'mots_cles'):
                resume.mots_cles.all().delete()
                print("🔍 DEBUG: Mots-clés supprimés")

            # Puis supprimer le résumé
            resume.delete()
            print(f"✅ DEBUG: Résumé '{titre}' supprimé avec succès")
            messages.success(request, f'Résumé "{titre}" supprimé avec succès.')
        except Exception as e:
            print(f"❌ DEBUG: Erreur lors de la suppression : {str(e)}")
            messages.error(request, f'Erreur lors de la suppression : {str(e)}')

        return redirect('mes_transcriptions')

    # Si ce n'est pas une requête POST, rediriger vers mes transcriptions
    print("🔍 DEBUG: Requête GET reçue, redirection vers mes_transcriptions")
    return redirect('mes_transcriptions')


def voir_transcription(request, transcription_id):
    """Affiche une transcription spécifique."""
    transcription = get_object_or_404(Transcription, id=transcription_id, utilisateur=request.user)

    context = {
        'transcription': transcription,
    }
    return render(request, 'transcription_resume/voir_transcription.html', context)


def voir_resume(request, resume_id):
    """Affiche un résumé spécifique."""
    resume = get_object_or_404(Resume, id=resume_id, auteur=request.user)

    context = {
        'resume': resume,
    }
    return render(request, 'transcription_resume/voir_resume.html', context)


def exporter_transcription(request, transcription_id, format='pdf'):
    """Exporte une transcription en format PDF ou TXT."""
    transcription = get_object_or_404(Transcription, id=transcription_id, utilisateur=request.user)

    if format == 'txt':
        return exporter_transcription_txt(transcription)
    else:
        return exporter_transcription_pdf(transcription)


def exporter_transcription_txt(transcription):
    """Exporte une transcription en format TXT."""
    # Créer la réponse HTTP avec le contenu de la transcription
    response = HttpResponse(content_type='text/plain; charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename="transcription_{transcription.id}_{transcription.date_transcription.strftime("%Y%m%d_%H%M")}.txt"'

    # Contenu du fichier
    contenu = f"""TRANSCRIPTION
=============

Titre: {transcription.titre}
Date: {transcription.date_transcription.strftime("%d/%m/%Y à %H:%M")}
Utilisateur: {transcription.utilisateur.username}

CONTENU:
--------
{transcription.contenu}

---
Généré par l'application de transcription IA
"""

    response.write(contenu)
    return response


def exporter_transcription_pdf(transcription):
    """Exporte une transcription en format PDF."""
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
    from reportlab.lib import colors

    # Créer la réponse HTTP
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="transcription_{transcription.id}_{transcription.date_transcription.strftime("%Y%m%d_%H%M")}.pdf"'

    # Créer le document PDF
    doc = SimpleDocTemplate(response, pagesize=A4,
                          rightMargin=72, leftMargin=72,
                          topMargin=72, bottomMargin=18)

    # Styles
    styles = getSampleStyleSheet()

    # Style personnalisé pour le titre
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )

    # Style pour les métadonnées
    meta_style = ParagraphStyle(
        'MetaStyle',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=6,
        textColor=colors.darkgrey
    )

    # Style pour le contenu
    content_style = ParagraphStyle(
        'ContentStyle',
        parent=styles['Normal'],
        fontSize=11,
        alignment=TA_JUSTIFY,
        spaceAfter=12,
        leading=14
    )

    # Construire le contenu
    story = []

    # Titre
    story.append(Paragraph("📄 TRANSCRIPTION", title_style))
    story.append(Spacer(1, 20))

    # Métadonnées
    story.append(Paragraph(f"<b>Titre:</b> {transcription.titre}", meta_style))
    story.append(Paragraph(f"<b>Date:</b> {transcription.date_transcription.strftime('%d/%m/%Y à %H:%M')}", meta_style))
    story.append(Paragraph(f"<b>Utilisateur:</b> {transcription.utilisateur.username}", meta_style))
    story.append(Spacer(1, 20))

    # Ligne de séparation
    story.append(Paragraph("─" * 80, meta_style))
    story.append(Spacer(1, 20))

    # Contenu de la transcription
    story.append(Paragraph("<b>CONTENU:</b>", meta_style))
    story.append(Spacer(1, 10))

    # Diviser le contenu en paragraphes
    paragraphs = transcription.contenu.split('\n\n')
    for para in paragraphs:
        if para.strip():
            story.append(Paragraph(para.strip(), content_style))

    story.append(Spacer(1, 30))
    story.append(Paragraph("─" * 80, meta_style))
    story.append(Paragraph("<i>Généré par l'application de transcription IA</i>", meta_style))

    # Construire le PDF
    doc.build(story)
    return response


def exporter_resume(request, resume_id, format='pdf'):
    """Exporte un résumé en format PDF ou TXT."""
    resume = get_object_or_404(Resume, id=resume_id, auteur=request.user)

    if format == 'txt':
        return exporter_resume_txt(resume)
    else:
        return exporter_resume_pdf(resume)


def exporter_resume_txt(resume):
    """Exporte un résumé en format TXT."""
    # Créer la réponse HTTP avec le contenu du résumé
    response = HttpResponse(content_type='text/plain; charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename="resume_{resume.id}_{resume.date_creation.strftime("%Y%m%d_%H%M")}.txt"'

    # Contenu du fichier
    contenu = f"""RÉSUMÉ
======

Titre: {resume.titre}
Date: {resume.date_creation.strftime("%d/%m/%Y à %H:%M")}
Auteur: {resume.auteur.username}

CONTENU:
--------
{resume.contenu}

---
Généré par l'application de transcription IA
"""

    response.write(contenu)
    return response


def exporter_resume_pdf(resume):
    """Exporte un résumé en format PDF."""
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
    from reportlab.lib import colors

    # Créer la réponse HTTP
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="resume_{resume.id}_{resume.date_creation.strftime("%Y%m%d_%H%M")}.pdf"'

    # Créer le document PDF
    doc = SimpleDocTemplate(response, pagesize=A4,
                          rightMargin=72, leftMargin=72,
                          topMargin=72, bottomMargin=18)

    # Styles
    styles = getSampleStyleSheet()

    # Style personnalisé pour le titre
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkgreen
    )

    # Style pour les métadonnées
    meta_style = ParagraphStyle(
        'MetaStyle',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=6,
        textColor=colors.darkgrey
    )

    # Style pour le contenu
    content_style = ParagraphStyle(
        'ContentStyle',
        parent=styles['Normal'],
        fontSize=11,
        alignment=TA_JUSTIFY,
        spaceAfter=12,
        leading=14
    )

    # Construire le contenu
    story = []

    # Titre
    story.append(Paragraph("📋 RÉSUMÉ", title_style))
    story.append(Spacer(1, 20))

    # Métadonnées
    story.append(Paragraph(f"<b>Titre:</b> {resume.titre}", meta_style))
    story.append(Paragraph(f"<b>Date:</b> {resume.date_creation.strftime('%d/%m/%Y à %H:%M')}", meta_style))
    story.append(Paragraph(f"<b>Auteur:</b> {resume.auteur.username}", meta_style))
    story.append(Spacer(1, 20))

    # Ligne de séparation
    story.append(Paragraph("─" * 80, meta_style))
    story.append(Spacer(1, 20))

    # Contenu du résumé
    story.append(Paragraph("<b>CONTENU:</b>", meta_style))
    story.append(Spacer(1, 10))

    # Diviser le contenu en paragraphes
    paragraphs = resume.contenu.split('\n\n')
    for para in paragraphs:
        if para.strip():
            story.append(Paragraph(para.strip(), content_style))

    story.append(Spacer(1, 30))
    story.append(Paragraph("─" * 80, meta_style))
    story.append(Paragraph("<i>Généré par l'application de transcription IA</i>", meta_style))

    # Construire le PDF
    doc.build(story)
    return response

def exporter_tout_pdf(request):
    """Exporte toutes les transcriptions et résumés de l'utilisateur en un seul PDF."""
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
    from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT
    from reportlab.lib import colors
    import datetime

    # Récupérer toutes les transcriptions et résumés de l'utilisateur
    transcriptions = Transcription.objects.filter(utilisateur=request.user).order_by('-date_transcription')
    resumes = Resume.objects.filter(auteur=request.user).order_by('-date_creation')

    if not transcriptions and not resumes:
        return HttpResponse("Aucune transcription ou résumé trouvé pour cet utilisateur.", status=404)

    # Créer la réponse HTTP
    response = HttpResponse(content_type='application/pdf')
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    response['Content-Disposition'] = f'attachment; filename="export_complet_{request.user.username}_{timestamp}.pdf"'

    # Créer le document PDF
    doc = SimpleDocTemplate(response, pagesize=A4,
                          rightMargin=72, leftMargin=72,
                          topMargin=72, bottomMargin=18)

    # Styles
    styles = getSampleStyleSheet()

    # Style pour le titre principal
    main_title_style = ParagraphStyle(
        'MainTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue,
        fontName='Helvetica-Bold'
    )

    # Style pour les titres de section
    section_title_style = ParagraphStyle(
        'SectionTitle',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=20,
        spaceBefore=30,
        alignment=TA_LEFT,
        textColor=colors.darkgreen,
        fontName='Helvetica-Bold'
    )

    # Style pour les titres d'éléments
    item_title_style = ParagraphStyle(
        'ItemTitle',
        parent=styles['Heading3'],
        fontSize=14,
        spaceAfter=10,
        spaceBefore=20,
        alignment=TA_LEFT,
        textColor=colors.darkred,
        fontName='Helvetica-Bold'
    )

    # Style pour les métadonnées
    meta_style = ParagraphStyle(
        'MetaStyle',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6,
        textColor=colors.darkgrey
    )

    # Style pour le contenu
    content_style = ParagraphStyle(
        'ContentStyle',
        parent=styles['Normal'],
        fontSize=11,
        alignment=TA_JUSTIFY,
        spaceAfter=12,
        leading=14
    )

    # Construire le contenu
    story = []

    # Page de titre
    story.append(Paragraph("📚 EXPORT COMPLET", main_title_style))
    story.append(Spacer(1, 20))
    story.append(Paragraph(f"Utilisateur: {request.user.username}", meta_style))
    story.append(Paragraph(f"Date d'export: {datetime.datetime.now().strftime('%d/%m/%Y à %H:%M')}", meta_style))
    story.append(Paragraph(f"Nombre de transcriptions: {transcriptions.count()}", meta_style))
    story.append(Paragraph(f"Nombre de résumés: {resumes.count()}", meta_style))
    story.append(Spacer(1, 40))

    # Table des matières
    story.append(Paragraph("📋 TABLE DES MATIÈRES", section_title_style))
    story.append(Spacer(1, 10))

    # Transcriptions dans la table des matières
    if transcriptions:
        story.append(Paragraph("<b>TRANSCRIPTIONS:</b>", meta_style))
        for i, transcription in enumerate(transcriptions, 1):
            story.append(Paragraph(f"{i}. {transcription.titre} - {transcription.date_transcription.strftime('%d/%m/%Y')}", meta_style))
        story.append(Spacer(1, 10))

    # Résumés dans la table des matières
    if resumes:
        story.append(Paragraph("<b>RÉSUMÉS:</b>", meta_style))
        for i, resume in enumerate(resumes, 1):
            story.append(Paragraph(f"{i}. {resume.titre} - {resume.date_creation.strftime('%d/%m/%Y')}", meta_style))

    story.append(PageBreak())

    # Section Transcriptions
    if transcriptions:
        story.append(Paragraph("📄 TRANSCRIPTIONS", section_title_style))
        story.append(Spacer(1, 20))

        for i, transcription in enumerate(transcriptions, 1):
            story.append(Paragraph(f"{i}. {transcription.titre}", item_title_style))
            story.append(Paragraph(f"Date: {transcription.date_transcription.strftime('%d/%m/%Y à %H:%M')}", meta_style))
            story.append(Spacer(1, 10))

            # Contenu de la transcription
            paragraphs = transcription.contenu.split('\n\n')
            for para in paragraphs:
                if para.strip():
                    story.append(Paragraph(para.strip(), content_style))

            story.append(Spacer(1, 20))
            story.append(Paragraph("─" * 80, meta_style))
            story.append(Spacer(1, 20))

        story.append(PageBreak())

    # Section Résumés
    if resumes:
        story.append(Paragraph("📋 RÉSUMÉS", section_title_style))
        story.append(Spacer(1, 20))

        for i, resume in enumerate(resumes, 1):
            story.append(Paragraph(f"{i}. {resume.titre}", item_title_style))
            story.append(Paragraph(f"Date: {resume.date_creation.strftime('%d/%m/%Y à %H:%M')}", meta_style))
            story.append(Spacer(1, 10))

            # Contenu du résumé
            paragraphs = resume.contenu.split('\n\n')
            for para in paragraphs:
                if para.strip():
                    story.append(Paragraph(para.strip(), content_style))

            story.append(Spacer(1, 20))
            story.append(Paragraph("─" * 80, meta_style))
            story.append(Spacer(1, 20))

    # Pied de page
    story.append(PageBreak())
    story.append(Paragraph("📊 STATISTIQUES", section_title_style))
    story.append(Spacer(1, 10))
    story.append(Paragraph(f"Total de transcriptions: {transcriptions.count()}", meta_style))
    story.append(Paragraph(f"Total de résumés: {resumes.count()}", meta_style))
    if transcriptions:
        story.append(Paragraph(f"Première transcription: {transcriptions.last().date_transcription.strftime('%d/%m/%Y')}", meta_style))
        story.append(Paragraph(f"Dernière transcription: {transcriptions.first().date_transcription.strftime('%d/%m/%Y')}", meta_style))
    story.append(Spacer(1, 30))
    story.append(Paragraph("─" * 80, meta_style))
    story.append(Paragraph("<i>Généré par l'application de transcription IA</i>", meta_style))

    # Construire le PDF
    doc.build(story)
    return response


def exporter_transcriptions_zip(request):
    """Exporte toutes les transcriptions de l'utilisateur en fichiers TXT dans un ZIP."""
    import zipfile
    from io import BytesIO
    import datetime

    # Récupérer toutes les transcriptions de l'utilisateur
    transcriptions = Transcription.objects.filter(utilisateur=request.user).order_by('-date_transcription')

    if not transcriptions:
        return HttpResponse("Aucune transcription trouvée pour cet utilisateur.", status=404)

    # Créer un fichier ZIP en mémoire
    zip_buffer = BytesIO()

    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for transcription in transcriptions:
            # Nom du fichier
            filename = f"transcription_{transcription.id}_{transcription.date_transcription.strftime('%Y%m%d_%H%M')}.txt"

            # Contenu du fichier
            contenu = f"""TRANSCRIPTION
=============

Titre: {transcription.titre}
Date: {transcription.date_transcription.strftime("%d/%m/%Y à %H:%M")}
Utilisateur: {transcription.utilisateur.username}

CONTENU:
--------
{transcription.contenu}

---
Généré par l'application de transcription IA
"""

            # Ajouter le fichier au ZIP
            zip_file.writestr(filename, contenu.encode('utf-8'))

    # Préparer la réponse
    zip_buffer.seek(0)
    response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    response['Content-Disposition'] = f'attachment; filename="transcriptions_{request.user.username}_{timestamp}.zip"'

    return response


def exporter_resumes_zip(request):
    """Exporte tous les résumés de l'utilisateur en fichiers TXT dans un ZIP."""
    import zipfile
    from io import BytesIO
    import datetime

    # Récupérer tous les résumés de l'utilisateur
    resumes = Resume.objects.filter(auteur=request.user).order_by('-date_creation')

    if not resumes:
        return HttpResponse("Aucun résumé trouvé pour cet utilisateur.", status=404)

    # Créer un fichier ZIP en mémoire
    zip_buffer = BytesIO()

    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for resume in resumes:
            # Nom du fichier
            filename = f"resume_{resume.id}_{resume.date_creation.strftime('%Y%m%d_%H%M')}.txt"

            # Contenu du fichier
            contenu = f"""RÉSUMÉ
======

Titre: {resume.titre}
Date: {resume.date_creation.strftime("%d/%m/%Y à %H:%M")}
Auteur: {resume.auteur.username}

CONTENU:
--------
{resume.contenu}

---
Généré par l'application de transcription IA
"""

            # Ajouter le fichier au ZIP
            zip_file.writestr(filename, contenu.encode('utf-8'))

    # Préparer la réponse
    zip_buffer.seek(0)
    response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    response['Content-Disposition'] = f'attachment; filename="resumes_{request.user.username}_{timestamp}.zip"'

    return response

@login_required
def exporter_transcription_pdf_direct(request, transcription_id):
    """Exporte directement une transcription en PDF."""
    return exporter_transcription(request, transcription_id, format='pdf')


@login_required
def exporter_resume_pdf_direct(request, resume_id):
    """Exporte directement un résumé en PDF."""
    return exporter_resume(request, resume_id, format='pdf')


@login_required
def exporter_transcription_txt_direct(request, transcription_id):
    """Exporte directement une transcription en TXT."""
    return exporter_transcription(request, transcription_id, format='txt')


@login_required
def exporter_resume_txt_direct(request, resume_id):
    """Exporte directement un résumé en TXT."""
    return exporter_resume(request, resume_id, format='txt')



def televersement_fichier(request):
    """Gère le téléversement de fichiers et la transcription initiale."""
    if request.method == 'POST':
        form = TeleversementFichierForm(request.POST, request.FILES, user=request.user)
        if form.is_valid():
            fichier = form.save()
            contenu_fichier = ""
            try:
                # Création du répertoire temp si inexistant
                temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
                os.makedirs(temp_dir, exist_ok=True)

                # Traitement des PDF
                if fichier.chemin.name.endswith('.pdf'):
                    logger.info(f"Traitement du PDF: {fichier.chemin.name}")
                    try:
                        with open(fichier.chemin.path, 'rb') as pdf_file:
                            pdf_reader = PdfReader(pdf_file)
                            for page_num in range(len(pdf_reader.pages)):
                                page = pdf_reader.pages[page_num]
                                contenu_fichier += page.extract_text() + "\n"
                    except Exception as e:
                        logger.error(f"Erreur lecture PDF: {str(e)}")
                        raise

                # Traitement des DOCX et DOC
                elif fichier.chemin.name.endswith('.docx') or fichier.chemin.name.endswith('.doc'):
                    print(f"Traitement du document Word: {fichier.chemin.name}")
                    try:
                        if fichier.chemin.name.endswith('.docx'):
                            # Traitement DOCX (nouveau format)
                            doc = docx.Document(fichier.chemin.path)
                            for paragraph in doc.paragraphs:
                                contenu_fichier += paragraph.text + "\n"
                        else:
                            # Traitement DOC (ancien format) - conversion en texte simple
                            try:
                                import subprocess
                                # Essayer avec antiword si disponible
                                result = subprocess.run(['antiword', fichier.chemin.path],
                                                      capture_output=True, text=True, timeout=30)
                                if result.returncode == 0:
                                    contenu_fichier = result.stdout
                                else:
                                    contenu_fichier = "Erreur: Impossible de lire ce fichier .doc. Veuillez le convertir en .docx ou .pdf"
                            except (subprocess.TimeoutExpired, FileNotFoundError):
                                # Fallback : message d'erreur informatif
                                contenu_fichier = "Erreur: Les fichiers .doc ne sont pas entièrement supportés. Veuillez convertir votre fichier en .docx ou .pdf pour une meilleure compatibilité."
                    except Exception as e:
                        print(f"Erreur lecture document Word: {str(e)}")
                        contenu_fichier = f"Erreur lors de la lecture du document Word: {str(e)}"

                # Traitement des fichiers audio/vidéo (MP4, MP3)
                elif any(fichier.chemin.name.endswith(ext) for ext in ['.mp4', '.mp3']):
                    audio_path_temp = None
                    wav_path_temp = None
                    try:
                        logger.info(f"Traitement fichier multimédia: {fichier.chemin.name}")

                        # Extraction audio depuis vidéo (version ultra-simple et compatible)
                        if fichier.chemin.name.endswith('.mp4'):
                            try:
                                print(f"Extraction audio depuis vidéo: {fichier.chemin.name}")
                                audio_path_temp = os.path.join(temp_dir, f"{fichier.id}_audio.wav")

                                print("Chargement de la vidéo...")
                                video = VideoFileClip(fichier.chemin.path)

                                # Vérifier que la vidéo a de l'audio
                                if video.audio is None:
                                    video.close()
                                    contenu_fichier = "Erreur: Cette vidéo ne contient pas de piste audio."
                                    print("Vidéo sans piste audio")
                                else:
                                    print("Vidéo avec audio détectée")
                                    print("Extraction audio en cours... Patientez...")

                                    # Extraction avec format compatible Google Speech Recognition
                                    try:
                                        # Paramètres optimisés pour la reconnaissance vocale
                                        video.audio.write_audiofile(
                                            audio_path_temp,
                                            codec='pcm_s16le',  # Format WAV compatible
                                            ffmpeg_params=['-ar', '16000', '-ac', '1']  # 16kHz mono
                                        )
                                        video.close()

                                        # Vérifier que le fichier a été créé
                                        if os.path.exists(audio_path_temp) and os.path.getsize(audio_path_temp) > 0:
                                            wav_path_temp = audio_path_temp
                                            print(f"Audio extrait avec succès: {audio_path_temp}")
                                        else:
                                            contenu_fichier = "Erreur: Le fichier audio extrait est vide."
                                            print("Fichier audio vide")
                                    except Exception as audio_error:
                                        video.close()
                                        print(f"Erreur lors de l'extraction audio avec paramètres: {str(audio_error)}")
                                        print("Tentative d'extraction simple...")

                                        # Fallback : extraction simple sans paramètres
                                        try:
                                            video = VideoFileClip(fichier.chemin.path)
                                            video.audio.write_audiofile(audio_path_temp)
                                            video.close()

                                            if os.path.exists(audio_path_temp) and os.path.getsize(audio_path_temp) > 0:
                                                wav_path_temp = audio_path_temp
                                                print(f"Audio extrait avec méthode simple: {audio_path_temp}")
                                            else:
                                                contenu_fichier = "Erreur: Le fichier audio extrait est vide."
                                        except Exception as simple_error:
                                            contenu_fichier = f"Erreur lors de l'extraction audio: {str(simple_error)}"

                            except Exception as e:
                                print(f"Erreur extraction audio vidéo: {str(e)}")
                                contenu_fichier = f"Erreur lors du traitement de la vidéo: {str(e)}"

                        # Conversion MP3 vers WAV
                        elif fichier.chemin.name.endswith('.mp3'):
                            try:
                                print(f"🎵 Traitement fichier MP3: {fichier.chemin.name}")
                                audio_path_temp = fichier.chemin.path
                                print(f"📁 Chemin MP3: {audio_path_temp}")

                                wav_path_temp = convertir_mp3_en_wav(audio_path_temp)

                                if not wav_path_temp:
                                    contenu_fichier = "Erreur: Impossible de convertir le fichier MP3 en WAV. Vérifiez que le fichier MP3 est valide et non corrompu."
                                    print("❌ Conversion MP3 vers WAV échouée")
                                elif not os.path.exists(wav_path_temp):
                                    contenu_fichier = "Erreur: Le fichier WAV converti n'a pas été créé. Problème de conversion MP3."
                                    print(f"❌ Fichier WAV non trouvé: {wav_path_temp}")
                                else:
                                    print(f"✅ Conversion MP3 vers WAV réussie: {wav_path_temp}")

                            except Exception as e:
                                logger.error(f"Erreur conversion MP3 vers WAV: {str(e)}")
                                contenu_fichier = f"Erreur lors de la conversion MP3: {str(e)}"
                                print(f"❌ Exception conversion MP3: {e}")
                                wav_path_temp = None

                        # Transcription audio (version simple avec conversion de format)
                        # Seulement si on n'a pas déjà un message d'erreur ET qu'on a un fichier audio
                        if not contenu_fichier and wav_path_temp and os.path.exists(wav_path_temp):
                            print("🎵 Début transcription audio...")
                            try:
                                # Vérifier la taille du fichier audio
                                file_size = os.path.getsize(wav_path_temp)
                                print(f"📊 Taille du fichier audio: {file_size} bytes")

                                if file_size == 0:
                                    contenu_fichier = "Erreur: Le fichier audio extrait est vide."
                                else:
                                    # Essayer la transcription directe d'abord
                                    print("🔄 Transcription en cours... Cela peut prendre du temps.")
                                    contenu_fichier = transcrire_segment_simple(wav_path_temp)

                                    # Si la transcription directe échoue, essayer par segments
                                    if not contenu_fichier or not contenu_fichier.strip():
                                        print("🔄 Transcription directe échouée, essai par segments...")
                                        contenu_fichier = transcrire_audio_par_segments(wav_path_temp, segment_duration=30)

                                    # Vérifier le résultat final
                                    if not contenu_fichier or not contenu_fichier.strip():
                                        contenu_fichier = "Transcription vide. Vérifiez que l'audio contient de la parole claire en français."
                                    else:
                                        print(f"✅ Transcription réussie: {len(contenu_fichier)} caractères")

                            except sr.UnknownValueError:
                                contenu_fichier = "Impossible de transcrire l'audio. Causes possibles:\n- Pas de parole détectée\n- Qualité audio insuffisante\n- Audio trop faible ou bruité\n- Langue non reconnue"
                                print("⚠️ Reconnaissance vocale: pas de parole détectée")

                            except sr.RequestError as e:
                                error_msg = str(e).lower()
                                if "bad request" in error_msg:
                                    print("❌ Bad Request détecté - tentative de conversion de format...")

                                    # Essayer de convertir le format audio
                                    audio_converted_path = os.path.join(temp_dir, f"{fichier.id}_audio_converted.wav")

                                    if convertir_audio_format_compatible(wav_path_temp, audio_converted_path):
                                        print("🔄 Format converti, nouvelle tentative de transcription...")
                                        try:
                                            contenu_fichier = transcrire_segment_simple(audio_converted_path)
                                            if not contenu_fichier or not contenu_fichier.strip():
                                                contenu_fichier = transcrire_audio_par_segments(audio_converted_path, segment_duration=30)

                                            if contenu_fichier and contenu_fichier.strip():
                                                print(f"✅ Transcription réussie après conversion: {len(contenu_fichier)} caractères")
                                            else:
                                                contenu_fichier = "Transcription échouée même après conversion de format. Vérifiez que l'audio contient de la parole claire."
                                        except Exception as convert_error:
                                            contenu_fichier = f"Erreur lors de la transcription après conversion: {str(convert_error)}"
                                        finally:
                                            # Nettoyer le fichier converti
                                            if os.path.exists(audio_converted_path):
                                                try:
                                                    os.remove(audio_converted_path)
                                                except:
                                                    pass
                                    else:
                                        contenu_fichier = "Erreur de format audio. Le fichier audio n'est pas compatible avec le service de reconnaissance vocale. Solutions:\n\n1. Vérifiez que votre vidéo MP4 contient bien de l'audio\n2. Essayez avec une vidéo plus courte (moins de 2 minutes)\n3. Assurez-vous que l'audio est de bonne qualité\n4. Vérifiez que l'audio contient de la parole claire en français\n\nDétails techniques: Le service de reconnaissance vocale a rejeté le fichier audio extrait de votre vidéo."
                                elif "quota" in error_msg or "limit" in error_msg:
                                    contenu_fichier = "Limite de quota atteinte pour le service de reconnaissance vocale. Veuillez réessayer plus tard."
                                else:
                                    contenu_fichier = f"Erreur de connexion au service de reconnaissance vocale: {e}\n\nConseils:\n- Vérifiez votre connexion internet\n- Réessayez avec un fichier audio plus court\n- Assurez-vous que l'audio contient de la parole claire"
                                print(f"❌ Erreur API reconnaissance vocale: {e}")

                            except Exception as e:
                                contenu_fichier = f"Erreur inattendue lors de la transcription: {str(e)}"
                                print(f"❌ Erreur reconnaissance vocale: {str(e)}")

                        # Seulement afficher ce message si on n'a aucun contenu ET aucun message d'erreur spécifique
                        elif not contenu_fichier:
                            if not wav_path_temp:
                                contenu_fichier = "Erreur: Impossible d'extraire l'audio de cette vidéo. Vérifiez que la vidéo contient bien de l'audio."
                            else:
                                contenu_fichier = "Erreur: Fichier audio temporaire non créé. Vérifiez que votre vidéo contient de l'audio et réessayez."
                            print("❌ Aucun fichier audio disponible pour transcription")

                    finally:
                        # Nettoyage des fichiers temporaires
                        for temp_file in [audio_path_temp, wav_path_temp]:
                            if temp_file and temp_file != fichier.chemin.path and os.path.exists(temp_file):
                                try:
                                    os.remove(temp_file)
                                except Exception as e:
                                    logger.warning(f"Erreur suppression fichier temporaire {temp_file}: {e}")

                # Traitement par défaut pour les fichiers texte
                else:
                    logger.info(f"Traitement fichier texte: {fichier.chemin.name}")
                    try:
                        with open(fichier.chemin.path, 'rb') as f:
                            try:
                                contenu_fichier = f.read().decode('utf-8')
                            except UnicodeDecodeError:
                                f.seek(0)
                                contenu_fichier = f.read().decode('latin-1', errors='ignore')
                                logger.warning("Décodage UTF-8 échoué, utilisation de latin-1")
                    except Exception as e:
                        logger.error(f"Erreur lecture fichier texte: {str(e)}")
                        raise

                # Création de la transcription
                logger.info("Création de l'objet Transcription")

                # Gérer l'utilisateur pour la transcription
                if request.user.is_authenticated:
                    utilisateur = request.user
                else:
                    # Créer un utilisateur par défaut ou utiliser un utilisateur existant
                    from django.contrib.auth.models import User
                    utilisateur, created = User.objects.get_or_create(
                        username='anonyme',
                        defaults={'email': '<EMAIL>'}
                    )

                transcription_obj = Transcription.objects.create(
                    titre=f"Transcription de {fichier.nom}",
                    contenu=contenu_fichier,
                    utilisateur=utilisateur
                )

                # Génération du résumé initial
                logger.info("Génération du résumé")
                try:
                    if contenu_fichier.strip():
                        parser = PlaintextParser.from_string(contenu_fichier, Tokenizer("fr"))
                        stemmer = Stemmer("fr")
                        summarizer = LsaSummarizer(stemmer)
                        summary_sentences = summarizer(parser.document, 3)
                        resume_contenu = " ".join(str(sentence) for sentence in summary_sentences)
                    else:
                        resume_contenu = "Le contenu est vide, pas de résumé généré."

                    Resume.objects.create(
                        titre=f"Résumé de {fichier.nom}",
                        contenu=resume_contenu,
                        auteur=utilisateur
                    )
                except Exception as e:
                    logger.error(f"Erreur génération résumé: {str(e)}")
                    Resume.objects.create(
                        titre=f"Résumé de {fichier.nom} (Erreur)",
                        contenu=f"Erreur lors de la génération du résumé initial: {e}",
                        auteur=utilisateur
                    )

                logger.info(f"Traitement terminé pour le fichier {fichier.id}")
                return redirect('consulter_fichier', fichier_id=fichier.id)

            except Exception as e:
                logger.error(f"Erreur majeure traitement fichier: {str(e)}", exc_info=True)
                # Nettoyage en cas d'erreur
                if fichier.chemin and os.path.exists(fichier.chemin.path):
                    try:
                        os.remove(fichier.chemin.path)
                    except Exception as delete_error:
                        logger.error(f"Erreur suppression fichier: {delete_error}")
                fichier.delete()
                return HttpResponseBadRequest(
                    f"Erreur lors du traitement du fichier. Veuillez réessayer avec un autre fichier. Détails: {str(e)}"
                )
        else:
            logger.error(f"Erreur formulaire: {form.errors}")
            return render(request, 'transcription_resume/televersement.html', {
                'form': form,
                'errors': form.errors
            })
    else:
        form = TeleversementFichierForm(user=request.user)

    return render(request, 'transcription_resume/televersement.html', {'form': form})

def consulter_fichier(request, fichier_id):
    """Affiche la transcription, le résumé initial, les mots-clés et idées principales."""
    fichier = get_object_or_404(Fichier, id=fichier_id)

    # Chercher les transcriptions et résumés liés à ce fichier par titre ou utilisateur
    # Comme il n'y a plus de relation directe, on cherche par nom de fichier dans le titre
    transcription = Transcription.objects.filter(
        titre__icontains=fichier.nom
    ).first()

    resume = Resume.objects.filter(
        titre__icontains=fichier.nom
    ).first()

    contenu_fichier = transcription.contenu if transcription else ""

    # Générer les mots-clés et idées principales par défaut lors de la consultation
    # Vous pouvez choisir d'utiliser local ou OpenAI ici pour l'affichage initial
    analyse_data = analyser_texte_local(contenu_fichier)
    mots_cles = analyse_data.get('mots_cles', [])
    idees_principales = analyse_data.get('idees_principales', [])

    context = {
        'fichier': fichier,
        'transcription': transcription,
        'resume': resume,
        'mots_cles': mots_cles,
        'idees_principales': idees_principales,
    }
    return render(request, 'transcription_resume/consultation.html', context)

@csrf_exempt
def generer_resume(request, fichier_id):
    """
    Génère un résumé à la demande de l'utilisateur,
    en utilisant OpenAI ou une méthode locale.
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            texte_transcription = data.get('texte', '')
            resume_type = data.get('resume_type', 'facile') # 'facile' ou 'professionnel'
            use_ai_model = data.get('use_ai_model', False) # Utiliser les fonctions locales par défaut

            if not texte_transcription:
                return JsonResponse({'error': 'Aucun texte fourni pour le résumé.'}, status=400)

            resume_genere = None
            if use_ai_model:
                try:
                    resume_genere = generer_resume_openai(texte_transcription, resume_type)
                except Exception as e:
                    # Gérer spécifiquement l'erreur de quota ici pour un message client clair
                    if "Quota OpenAI dépassé" in str(e):
                        return JsonResponse({'error': str(e)}, status=429) # 429 Too Many Requests
                    return JsonResponse({'error': f"Erreur lors de la génération du résumé avec l'IA: {str(e)}"}, status=500)
            else:
                # Adaptez le nombre de phrases pour les types de résumé local
                print(f"🔄 Génération résumé local, type: {resume_type}")
                print(f"📊 Longueur texte: {len(texte_transcription)} caractères")

                if resume_type == 'facile':
                    resume_genere = generer_resume_local(texte_transcription, nombre_de_phrases=8)  # Plus de phrases pour un vrai résumé
                else: # professionnel
                    resume_genere = generer_resume_local(texte_transcription, nombre_de_phrases=12)  # Encore plus pour professionnel

                print(f"✅ Résumé généré: {len(resume_genere) if resume_genere else 0} caractères")
                print(f"📝 Aperçu résumé: {resume_genere[:100] if resume_genere else 'VIDE'}...")
            if resume_genere:
                fichier = get_object_or_404(Fichier, id=fichier_id)

                # Gérer l'utilisateur pour le résumé
                if request.user.is_authenticated:
                    utilisateur = request.user
                else:
                    from django.contrib.auth.models import User
                    utilisateur, created = User.objects.get_or_create(
                        username='anonyme',
                        defaults={'email': '<EMAIL>'}
                    )

                # Créer un titre unique avec timestamp pour éviter les doublons
                import datetime
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                titre_unique = f"Résumé de {fichier.nom} - {timestamp}"

                # Vérifier s'il existe déjà un résumé récent pour ce fichier et cet utilisateur
                resume_existant = Resume.objects.filter(
                    titre__startswith=f"Résumé de {fichier.nom}",
                    auteur=utilisateur
                ).order_by('-date_creation').first()

                # Si un résumé existe et a été créé il y a moins de 5 minutes, le mettre à jour
                if resume_existant:
                    time_diff = datetime.datetime.now(datetime.timezone.utc) - resume_existant.date_creation
                    if time_diff.total_seconds() < 300:  # 5 minutes
                        resume_existant.contenu = resume_genere
                        resume_existant.save()
                    else:
                        # Créer un nouveau résumé avec titre unique
                        Resume.objects.create(
                            titre=titre_unique,
                            contenu=resume_genere,
                            auteur=utilisateur
                        )
                else:
                    # Créer un nouveau résumé
                    Resume.objects.create(
                        titre=titre_unique,
                        contenu=resume_genere,
                        auteur=utilisateur
                    )
                return JsonResponse({'resume': resume_genere})
            else:
                return JsonResponse({'error': 'La génération du résumé a échoué.'}, status=500)

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Requête JSON invalide.'}, status=400)
        except Exception as e:
            print(f"Erreur inattendue dans generer_resume: {e}")
            return JsonResponse({'error': f"Une erreur interne est survenue : {str(e)}"}, status=500)
    return JsonResponse({'error': 'Méthode non autorisée.'}, status=405)
@csrf_exempt
def analyser_texte_ajax(request, fichier_id):
    """
    Analyse le texte à la demande de l'utilisateur,
    en utilisant OpenAI ou une méthode locale.
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            texte = data.get('texte', '')
            use_ai_model = data.get('use_ai_model', False)  # Utiliser les fonctions locales par défaut
            if not texte:
                return JsonResponse({'error': 'Le texte à analyser est vide.'}, status=400)

            analyse_resultat = {}
            if use_ai_model:
                analyse_resultat = analyser_texte_openai(texte)
            else:
                print(f"🔄 Analyse locale du texte")
                print(f"📊 Longueur texte: {len(texte)} caractères")
                analyse_resultat = analyser_texte_local(texte, num_mots_cles=20, num_idees_principales=8)  # Plus d'éléments
                print(f"✅ Analyse terminée")
                print(f"📝 Mots-clés trouvés: {len(analyse_resultat.get('mots_cles', []))}")
                print(f"📝 Idées principales: {len(analyse_resultat.get('idees_principales', []))}")
                print(f"🔍 Aperçu mots-clés: {analyse_resultat.get('mots_cles', [])[:5]}")
                print(f"🔍 Aperçu idées: {analyse_resultat.get('idees_principales', [])[:2]}")
            if 'error' in analyse_resultat:
                status_code = 500
                if "Quota OpenAI dépassé" in analyse_resultat['error']:
                    status_code = 429
                return JsonResponse({'error': analyse_resultat['error']}, status=status_code)

            # Sauvegarder l'analyse dans la base de données
            try:
                fichier = get_object_or_404(Fichier, pk=fichier_id)

                # Chercher la transcription liée à ce fichier
                transcription = Transcription.objects.filter(
                    titre__icontains=fichier.nom
                ).first()

                if transcription:
                    # Vérifier si une analyse existe déjà pour cette transcription
                    analyse_existante = MotsCles.objects.filter(
                        transcription=transcription,
                        utilisateur=request.user
                    ).first()

                    mots_cles_str = ', '.join(analyse_resultat.get('mots_cles', []))
                    idees_str = '\n'.join(analyse_resultat.get('idees_principales', []))

                    if analyse_existante:
                        # Mettre à jour l'analyse existante
                        analyse_existante.mots_cles_principaux = mots_cles_str
                        analyse_existante.idees_principales = idees_str
                        analyse_existante.save()
                    else:
                        # Créer une nouvelle analyse
                        MotsCles.objects.create(
                            transcription=transcription,
                            utilisateur=request.user,
                            mots_cles_principaux=mots_cles_str,
                            idees_principales=idees_str
                        )
            except Exception as e:
                print(f"Erreur lors de la sauvegarde de l'analyse: {e}")

            return JsonResponse({
                'mots_cles': analyse_resultat.get('mots_cles', []),
                'idees_principales': analyse_resultat.get('idees_principales', [])
            })
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Données JSON invalides'}, status=400)
        except Exception as e:
            print(f"Erreur lors de l'analyse du texte: {e}")
            return JsonResponse({'error': f'Erreur lors de l’analyse du texte: {e}'}, status=500)
    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
def exporter_fichier(request, fichier_id, format='pdf', content_type='transcription'):
    """Exporte la transcription ou le résumé au format PDF ou TXT."""
    fichier = get_object_or_404(Fichier, id=fichier_id)
    transcription = getattr(fichier, 'transcription', None)
    resume = None
    try:
        resume = fichier.resumes.latest('date_creation')
    except Resume.DoesNotExist:
        pass
    contenu = ""
    filename_suffix = ""
    if content_type == 'transcription':
        contenu = transcription.contenu if transcription else "Transcription non disponible."
        filename_suffix = "_transcription"
    elif content_type == 'resume':
        contenu = resume.contenu if resume else "Résumé non disponible."
        filename_suffix = "_resume"
    else:
        return HttpResponseBadRequest("Type de contenu non supporté pour l'export.")
    if format == 'pdf':
        buffer = BytesIO()
        try:
            p = canvas.Canvas(buffer, pagesize=letter)
            p.setFont("Helvetica", 10)
            line_height = 12
            y_position = float(letter[1] - inch)
            textobject = p.beginText(inch, y_position)
            textobject.textLine(f"Fichier: {fichier.nom}")
            textobject.moveCursor(0, -line_height)

            if content_type == 'transcription':
                textobject.textLine("Transcription:")
            elif content_type == 'resume':
                textobject.textLine("Résumé de la vidéo:")
            textobject.moveCursor(0, -line_height)

            for line in simpleSplit(contenu, letter[0] - 2 * inch, "Helvetica", 10):
                textobject.textLine(line)
                textobject.moveCursor(0, -line_height)

            p.drawText(textobject)
            p.showPage()
            p.save()
            pdf = buffer.getvalue()
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{fichier.nom}{filename_suffix}.pdf"'
            response.write(pdf)
            return response
        except Exception as e:
            print(f"Erreur lors de la création du PDF: {e}")
            return HttpResponseBadRequest("Erreur lors de la création du PDF.")
    elif format == 'txt':
        response = HttpResponse(contenu, content_type='text/plain; charset=utf-8')
        response['Content-Disposition'] = f'attachment; filename="{fichier.nom}{filename_suffix}.txt"'
        return response
    else:
        return HttpResponseBadRequest("Format d'export non supporté.")
def enregistreur_audio_view(request):
    """Vue pour l'enregistreur audio."""
    return render(request, 'enregistreur_audio/enregistreur_audio.html')

@csrf_exempt # À utiliser avec prudence. Pour la production, envisagez d'utiliser le jeton CSRF dans les en-têtes.
def transcribe_audio_api(request):
    if request.method == 'POST':
        if 'audio' not in request.FILES:
            return JsonResponse({"error": "No audio file provided"}, status=400)

        audio_file = request.FILES['audio']
        language = request.POST.get('language', 'fr') # Accéder aux données du formulaire via request.POST

        # --- Votre logique réelle de transcription va ici ---
        # Vous allez probablement enregistrer l'audio_file dans un emplacement temporaire
        # puis le transmettre à votre modèle/service de transcription.
        # Exemple utilisant un espace réservé :
        print(f"Audio reçu : {audio_file.name}, Langue : {language}")
        transcription_result = f"Ceci est une transcription d'exemple pour l'audio en {language}."
        # --- Fin de la logique de transcription ---

        return JsonResponse({"transcription": transcription_result})
    return JsonResponse({"error": "Méthode de requête invalide"}, status=405)

def register_view(request):
    """Vue pour l'enregistrement des utilisateurs."""
    return render(request, 'registration/register.html')


@user_passes_test(is_admin)
def gestionnaire_utilisateurs(request):
    """Vue pour gérer les utilisateurs (admin seulement)."""
    from django.contrib.auth.models import User
    from django.db.models import Count, Q

    # Utiliser exactement la même logique que admin_transcriptions
    # Récupérer seulement les utilisateurs normaux (pas les admins) qui ont des données
    utilisateurs = User.objects.select_related().annotate(
        nb_transcriptions=Count('transcription'),
        nb_resumes=Count('resume'),
        nb_fichiers=Count('fichier'),
        nb_analyses=Count('motscles')
    ).filter(
        Q(nb_transcriptions__gt=0) | Q(nb_resumes__gt=0) | Q(nb_fichiers__gt=0),
        is_superuser=False  # Exclure les administrateurs
    ).order_by('-date_joined')

    # Statistiques globales (identiques à admin_transcriptions)
    stats_globales = {
        'total_utilisateurs': utilisateurs.count(),
        'total_transcriptions': Transcription.objects.filter(utilisateur__is_superuser=False).count(),
        'total_resumes': Resume.objects.filter(auteur__is_superuser=False).count(),
        'total_fichiers': Fichier.objects.filter(user__is_superuser=False).count(),
        'total_analyses': MotsCles.objects.filter(utilisateur__is_superuser=False).count(),
    }

    context = {
        'utilisateurs': utilisateurs,
        'stats_globales': stats_globales,
    }
    return render(request, 'transcription_resume/gestionnaire_utilisateurs.html', context)


@user_passes_test(is_admin)
def supprimer_utilisateur(request, user_id):
    """Vue pour supprimer un utilisateur."""
    from django.contrib.auth.models import User

    utilisateur = get_object_or_404(User, id=user_id)

    # Empêcher la suppression d'un admin
    if utilisateur.is_superuser:
        messages.error(request, 'Impossible de supprimer un administrateur !')
        return redirect('gestionnaire_utilisateurs')

    if request.method == 'POST':
        username = utilisateur.username
        utilisateur.delete()
        messages.success(request, f'Utilisateur "{username}" supprimé avec succès !')
        return redirect('gestionnaire_utilisateurs')

    # Calculer les données qui seront supprimées
    stats_suppression = {
        'transcriptions': utilisateur.transcription_set.count(),
        'resumes': utilisateur.resume_set.count(),
        'fichiers': utilisateur.fichier_set.count(),
        'analyses': utilisateur.motscles_set.count(),
    }

    context = {
        'utilisateur': utilisateur,
        'stats_suppression': stats_suppression,
    }
    return render(request, 'transcription_resume/supprimer_utilisateur.html', context)


@user_passes_test(is_admin)
def generer_rapport_transcription(request, transcription_id):
    """Vue pour générer un rapport détaillé sur une transcription."""
    from django.utils import timezone

    transcription = get_object_or_404(Transcription, id=transcription_id)
    utilisateur = transcription.utilisateur

    # Traitement du formulaire de commentaire admin
    commentaire_admin = ""
    if request.method == 'POST':
        commentaire_admin = request.POST.get('commentaire_admin', '')

        # Si demande de téléchargement PDF
        if 'telecharger_pdf' in request.POST:
            return generer_rapport_pdf(transcription, commentaire_admin, request.user)

    # Récupérer les analyses associées à cette transcription
    analyses = MotsCles.objects.filter(transcription=transcription)

    # Récupérer les résumés liés à cette transcription via les analyses
    resumes_lies = []
    for analyse in analyses:
        if analyse.resume:
            resumes_lies.append(analyse.resume)

    # Supprimer les doublons
    resumes = list(set(resumes_lies))

    # Statistiques de la transcription
    stats_transcription = {
        'nb_mots': len(transcription.contenu.split()) if transcription.contenu else 0,
        'nb_caracteres': len(transcription.contenu) if transcription.contenu else 0,
        'nb_lignes': transcription.contenu.count('\n') + 1 if transcription.contenu else 0,
        'nb_analyses': analyses.count(),
        'nb_resumes': len(resumes),
        'duree_depuis_creation': (timezone.now() - transcription.date_transcription).days,
    }

    # Informations sur l'utilisateur
    stats_utilisateur = {
        'total_transcriptions': utilisateur.transcription_set.count(),
        'total_resumes': utilisateur.resume_set.count(),
        'total_fichiers': utilisateur.fichier_set.count(),
        'total_analyses': utilisateur.motscles_set.count(),
        'membre_depuis': (timezone.now() - utilisateur.date_joined).days,
        'derniere_connexion': utilisateur.last_login,
    }

    context = {
        'transcription': transcription,
        'analyses': analyses,
        'resumes': resumes,
        'stats_transcription': stats_transcription,
        'stats_utilisateur': stats_utilisateur,
        'utilisateur': utilisateur,
        'date_rapport': timezone.now(),
        'commentaire_admin': commentaire_admin,
    }

    return render(request, 'transcription_resume/rapport_transcription.html', context)


def generer_rapport_pdf(transcription, commentaire_admin, admin_user):
    """Génère un rapport PDF détaillé pour une transcription."""
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT
    from reportlab.lib import colors
    from reportlab.lib.units import inch
    from django.utils import timezone
    from django.http import HttpResponse

    # Créer la réponse HTTP
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="rapport_transcription_{transcription.id}_{timezone.now().strftime("%Y%m%d_%H%M")}.pdf"'

    # Créer le document PDF
    doc = SimpleDocTemplate(response, pagesize=A4,
                          rightMargin=72, leftMargin=72,
                          topMargin=72, bottomMargin=72)

    # Styles
    styles = getSampleStyleSheet()

    # Styles personnalisés
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=20,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )

    section_style = ParagraphStyle(
        'SectionStyle',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=15,
        spaceBefore=20,
        textColor=colors.darkgreen
    )

    normal_style = ParagraphStyle(
        'NormalStyle',
        parent=styles['Normal'],
        fontSize=11,
        alignment=TA_JUSTIFY,
        spaceAfter=12,
        leading=14
    )

    # Construire le contenu
    story = []

    # Titre du rapport
    story.append(Paragraph("📊 RAPPORT DE TRANSCRIPTION", title_style))
    story.append(Spacer(1, 20))

    # Informations générales
    info_data = [
        ['📅 Date du rapport:', timezone.now().strftime('%d/%m/%Y à %H:%M')],
        ['👤 Généré par:', f"{admin_user.username} (Administrateur)"],
        ['🆔 ID Transcription:', str(transcription.id)],
        ['📝 Titre:', transcription.titre],
        ['👤 Utilisateur:', transcription.utilisateur.username],
        ['📅 Date création:', transcription.date_transcription.strftime('%d/%m/%Y à %H:%M')],
    ]

    info_table = Table(info_data, colWidths=[2*inch, 4*inch])
    info_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(info_table)
    story.append(Spacer(1, 30))

    # Statistiques de la transcription
    story.append(Paragraph("📊 Statistiques de la Transcription", section_style))

    stats_data = [
        ['Nombre de mots:', str(len(transcription.contenu.split()) if transcription.contenu else 0)],
        ['Nombre de caractères:', str(len(transcription.contenu) if transcription.contenu else 0)],
        ['Nombre de lignes:', str(transcription.contenu.count('\n') + 1 if transcription.contenu else 0)],
    ]

    stats_table = Table(stats_data, colWidths=[2*inch, 1*inch])
    stats_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(stats_table)
    story.append(Spacer(1, 20))

    # Contenu de la transcription
    story.append(Paragraph("📝 Contenu de la Transcription", section_style))
    contenu_text = transcription.contenu[:2000] + "..." if len(transcription.contenu) > 2000 else transcription.contenu
    story.append(Paragraph(contenu_text or "Aucun contenu disponible", normal_style))
    story.append(Spacer(1, 20))

    # Analyses associées
    analyses = MotsCles.objects.filter(transcription=transcription)
    if analyses.exists():
        story.append(Paragraph("🔍 Analyses de Texte", section_style))
        for analyse in analyses:
            story.append(Paragraph(f"<b>Mots-clés:</b> {analyse.mots_cles_principaux or 'Aucun'}", normal_style))
            story.append(Paragraph(f"<b>Idées principales:</b> {analyse.idees_principales or 'Aucune'}", normal_style))
            story.append(Spacer(1, 10))

    # Commentaire de l'administrateur
    if commentaire_admin:
        story.append(Paragraph("💬 Commentaire de l'Administrateur", section_style))
        story.append(Paragraph(commentaire_admin, normal_style))
        story.append(Spacer(1, 20))

    # Signature
    story.append(Spacer(1, 30))
    story.append(Paragraph("─" * 80, normal_style))
    story.append(Paragraph(f"<i>Rapport généré le {timezone.now().strftime('%d/%m/%Y à %H:%M')} par {admin_user.username}</i>", normal_style))

    # Construire le PDF
    doc.build(story)
    return response


@user_passes_test(is_admin)
def rapport_utilisateur(request):
    """Vue pour créer un rapport d'évaluation pour un utilisateur."""
    from django.contrib.auth.models import User
    from django.utils import timezone

    # Récupérer tous les utilisateurs non-admin
    utilisateurs = User.objects.filter(is_superuser=False).order_by('username')

    if request.method == 'POST':
        user_id = request.POST.get('utilisateur_id')
        progression = request.POST.get('progression')
        commentaire = request.POST.get('commentaire')

        if user_id and progression and commentaire:
            utilisateur_selectionne = get_object_or_404(User, id=user_id, is_superuser=False)

            # Si demande de téléchargement PDF
            if 'telecharger_pdf' in request.POST:
                return generer_rapport_utilisateur_pdf(utilisateur_selectionne, progression, commentaire, request.user)

            # Si demande d'envoi par email
            if 'envoyer_email' in request.POST:
                success = envoyer_rapport_par_email(utilisateur_selectionne, progression, commentaire, request.user)
                context = {
                    'utilisateurs': utilisateurs,
                    'utilisateur_selectionne': utilisateur_selectionne,
                    'progression_selectionnee': progression,
                    'commentaire_saisi': commentaire,
                    'rapport_genere': True,
                    'email_envoye': success,
                }
                return render(request, 'transcription_resume/rapport_utilisateur.html', context)

            # Sinon, afficher le formulaire avec les données saisies
            context = {
                'utilisateurs': utilisateurs,
                'utilisateur_selectionne': utilisateur_selectionne,
                'progression_selectionnee': progression,
                'commentaire_saisi': commentaire,
                'rapport_genere': True,
            }
            return render(request, 'transcription_resume/rapport_utilisateur.html', context)

    context = {
        'utilisateurs': utilisateurs,
    }
    return render(request, 'transcription_resume/rapport_utilisateur.html', context)


def generer_rapport_utilisateur_pdf(utilisateur, progression, commentaire, admin_user):
    """Génère un rapport PDF d'évaluation pour un utilisateur."""
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT
    from reportlab.lib import colors
    from reportlab.lib.units import inch
    from django.utils import timezone
    from django.http import HttpResponse

    # Créer la réponse HTTP
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="rapport_evaluation_{utilisateur.username}_{timezone.now().strftime("%Y%m%d_%H%M")}.pdf"'

    # Créer le document PDF
    doc = SimpleDocTemplate(response, pagesize=A4,
                          rightMargin=72, leftMargin=72,
                          topMargin=72, bottomMargin=72)

    # Styles
    styles = getSampleStyleSheet()

    # Styles personnalisés
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=22,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )

    section_style = ParagraphStyle(
        'SectionStyle',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=15,
        spaceBefore=20,
        textColor=colors.darkgreen
    )

    normal_style = ParagraphStyle(
        'NormalStyle',
        parent=styles['Normal'],
        fontSize=12,
        alignment=TA_JUSTIFY,
        spaceAfter=12,
        leading=16
    )

    # Construire le contenu
    story = []

    # Titre du rapport
    story.append(Paragraph("📊 RAPPORT D'ÉVALUATION UTILISATEUR", title_style))
    story.append(Spacer(1, 30))

    # Informations générales
    info_data = [
        ['📅 Date du rapport:', timezone.now().strftime('%d/%m/%Y à %H:%M')],
        ['👤 Évalué par:', f"{admin_user.username} (Administrateur)"],
        ['🎯 Utilisateur évalué:', utilisateur.username],
        ['📧 Email:', utilisateur.email or 'Non renseigné'],
        ['📅 Inscrit le:', utilisateur.date_joined.strftime('%d/%m/%Y')],
        ['🕒 Dernière connexion:', utilisateur.last_login.strftime('%d/%m/%Y à %H:%M') if utilisateur.last_login else 'Jamais connecté'],
    ]

    info_table = Table(info_data, colWidths=[2.5*inch, 3.5*inch])
    info_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 11),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(info_table)
    story.append(Spacer(1, 30))

    # Statistiques de l'utilisateur
    story.append(Paragraph("📊 Statistiques d'Activité", section_style))

    stats_data = [
        ['📝 Transcriptions créées:', str(utilisateur.transcription_set.count())],
        ['📋 Résumés générés:', str(utilisateur.resume_set.count())],
        ['📁 Fichiers téléversés:', str(utilisateur.fichier_set.count())],
        ['🔍 Analyses effectuées:', str(utilisateur.motscles_set.count())],
    ]

    stats_table = Table(stats_data, colWidths=[3*inch, 1.5*inch])
    stats_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 11),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(stats_table)
    story.append(Spacer(1, 30))

    # Évaluation de la progression
    story.append(Paragraph("🎯 Évaluation de la Progression", section_style))

    # Couleur selon la progression
    progression_color = colors.green if progression == 'parfait' else colors.orange if progression == 'moyen' else colors.red
    progression_text = progression.upper()

    eval_data = [
        ['📈 Niveau de progression:', f"<font color='{progression_color.hexval()}'><b>{progression_text}</b></font>"],
    ]

    eval_table = Table(eval_data, colWidths=[3*inch, 2*inch])
    eval_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightyellow),
        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(eval_table)
    story.append(Spacer(1, 20))

    # Commentaire de l'administrateur
    story.append(Paragraph("💬 Commentaire de l'Administrateur", section_style))
    story.append(Paragraph(commentaire, normal_style))
    story.append(Spacer(1, 30))

    # Recommandations selon la progression
    story.append(Paragraph("📋 Recommandations", section_style))

    if progression == 'parfait':
        recommandations = "Excellent travail ! Continuez sur cette lancée. Vous maîtrisez parfaitement les outils de transcription et d'analyse."
    elif progression == 'moyen':
        recommandations = "Bon travail dans l'ensemble. Il y a encore quelques points à améliorer pour optimiser votre utilisation des outils."
    else:  # faible
        recommandations = "Des efforts supplémentaires sont nécessaires. N'hésitez pas à demander de l'aide pour mieux utiliser les fonctionnalités disponibles."

    story.append(Paragraph(recommandations, normal_style))
    story.append(Spacer(1, 30))

    # Signature
    story.append(Spacer(1, 30))
    story.append(Paragraph("─" * 80, normal_style))
    story.append(Paragraph(f"<i>Rapport d'évaluation généré le {timezone.now().strftime('%d/%m/%Y à %H:%M')} par {admin_user.username}</i>", normal_style))
    story.append(Paragraph(f"<i>Système de Transcription IA - Administration</i>", normal_style))

    # Construire le PDF
    doc.build(story)
    return response


def envoyer_rapport_par_email(utilisateur, progression, commentaire, admin_user):
    """Envoie le rapport d'évaluation par email à l'utilisateur."""
    from django.core.mail import EmailMessage
    from django.utils import timezone
    from django.conf import settings

    try:
        # Vérifier que l'utilisateur a un email
        if not utilisateur.email:
            return False

        # Générer le PDF en mémoire
        pdf_response = generer_rapport_utilisateur_pdf(utilisateur, progression, commentaire, admin_user)
        pdf_content = pdf_response.content

        # Créer le contenu de l'email
        sujet = f"📊 Rapport d'Évaluation - {utilisateur.username}"

        # Déterminer l'emoji et le message selon la progression
        if progression == 'parfait':
            emoji_progression = "🎉"
            message_progression = "Félicitations ! Votre progression est excellente."
        elif progression == 'moyen':
            emoji_progression = "👍"
            message_progression = "Bon travail ! Quelques améliorations sont possibles."
        else:  # faible
            emoji_progression = "💪"
            message_progression = "Continuez vos efforts ! Des améliorations sont nécessaires."

        # Corps de l'email
        corps_email = f"""
Bonjour {utilisateur.username},

{emoji_progression} Vous avez reçu un nouveau rapport d'évaluation de la part de l'administration.

📈 Évaluation de votre progression : {progression.upper()}
{message_progression}

💬 Commentaire de l'administrateur :
{commentaire}

📄 Vous trouverez en pièce jointe votre rapport d'évaluation détaillé au format PDF.

Ce rapport contient :
• Vos informations de compte
• Vos statistiques d'activité
• L'évaluation détaillée
• Des recommandations personnalisées

N'hésitez pas à nous contacter si vous avez des questions.

Cordialement,
L'équipe d'administration
Système de Transcription IA

---
Rapport généré le {timezone.now().strftime('%d/%m/%Y à %H:%M')} par {admin_user.username}
        """

        # Créer l'email
        email = EmailMessage(
            subject=sujet,
            body=corps_email,
            from_email=settings.DEFAULT_FROM_EMAIL if hasattr(settings, 'DEFAULT_FROM_EMAIL') else '<EMAIL>',
            to=[utilisateur.email],
            reply_to=[admin_user.email] if admin_user.email else None,
        )

        # Ajouter le PDF en pièce jointe
        nom_fichier = f"rapport_evaluation_{utilisateur.username}_{timezone.now().strftime('%Y%m%d_%H%M')}.pdf"
        email.attach(nom_fichier, pdf_content, 'application/pdf')

        # Envoyer l'email
        email.send()
        return True

    except Exception as e:
        # En cas d'erreur, on peut logger l'erreur
        print(f"Erreur lors de l'envoi de l'email : {e}")
        return False


def resume_analyse_view(request, fichier_id):
    """
    Vue générique pour afficher les détails du fichier, transcription,
    et préparer le contexte pour le résumé/analyse dynamique.
    """
    fichier = get_object_or_404(Fichier, pk=fichier_id)

    # Chercher la transcription liée à ce fichier par titre
    transcription = Transcription.objects.filter(
        titre__icontains=fichier.nom
    ).first()

    context = {
        'fichier': fichier,
        'transcription': transcription,
        'fichier_id': fichier_id,
    }
    return render(request, 'resume_analyse.html', context)


# === CHATBOT SUPPORT ===

# Configuration du chatbot
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions'
MODEL_NAME = 'deepseek/deepseek-chat'


@login_required
def support_chat_view(request):
    """
    Affiche la page de chat support et charge
    les anciens messages envoyés par l'utilisateur.
    """
    messages_user = ChatMessage.objects.filter(sender=request.user)
    return render(request, 'transcription_resume/support_chat.html', {
        'messages': messages_user
    })


@csrf_exempt
@login_required
def chatbot_view(request):
    """
    Point d'entrée AJAX pour envoyer le message à Deepseek
    et renvoyer la réponse JSON à l'interface.
    """
    if request.method == "POST":
        user_message = request.POST.get("message", "").strip()

        if not user_message:
            return JsonResponse({"reply": "Veuillez saisir un message."})

        if not OPENROUTER_API_KEY:
            return JsonResponse({"reply": "Service de chat temporairement indisponible. Veuillez réessayer plus tard."})

        payload = {
            "model": MODEL_NAME,
            "messages": [
                {"role": "system", "content": "Tu es un assistant SAV très intelligent pour une application de transcription et résumé de cours. Tu aides les utilisateurs avec leurs questions sur l'utilisation de l'application, les problèmes de transcription, la génération de résumés, l'exportation de fichiers, etc. Réponds de manière claire et utile en français."},
                {"role": "user", "content": user_message}
            ],
            "temperature": 0.7,
            "max_tokens": 500
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "X-Title": "Transcription Assistant"
        }

        try:
            response = requests.post(OPENROUTER_API_URL, headers=headers, json=payload)
            data = response.json()

            if response.status_code == 200:
                reply = data["choices"][0]["message"]["content"].strip()

                # Enregistrement en base
                ChatMessage.objects.create(
                    repair_request=None,
                    sender=request.user,
                    message=user_message,
                    response=reply
                )

                return JsonResponse({"reply": reply})
            else:
                error_message = "Désolé, je rencontre des difficultés techniques. Veuillez réessayer dans quelques instants."
                logger.error(f"Erreur API OpenRouter: {response.status_code} - {data}")
                return JsonResponse({"reply": error_message})

        except Exception as e:
            error_message = "Désolé, je ne peux pas répondre pour le moment. Veuillez réessayer plus tard."
            logger.error(f"Erreur chatbot: {str(e)}")
            return JsonResponse({"reply": error_message})

    return JsonResponse({"reply": "Méthode non autorisée"}, status=405)
