<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navbar Style</title>
    
    <!-- Lien vers le CSS de la navbar -->
    <link rel="stylesheet" href="transcription_resume/static/css/navbar-uniforme.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        /* Variables CSS pour la navbar */
        :root {
            --navbar-bg: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 50%, rgba(240, 147, 251, 0.95) 100%);
            --navbar-text: #ffffff;
            --navbar-text-hover: #f0f8ff;
            --navbar-transition: all 0.3s ease;
        }
        
        /* Style de base pour la navbar */
        .navbar-uniforme {
            background: var(--navbar-bg);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        /* Test de style forcé */
        .test-style {
            background: #ff0000 !important;
            color: white !important;
            padding: 10px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .content {
            margin-top: 100px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Test de la navbar -->
    <nav class="navbar-uniforme">
        <div class="navbar-container">
            <!-- Brand/Logo -->
            <a href="#" class="navbar-brand">
                <span class="brand-icon">🎤</span>
                Transcription IA
            </a>

            <!-- Navigation Links -->
            <ul class="navbar-nav">
                <li>
                    <a href="#" class="nav-link">
                        🏠 Accueil
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-link">
                        📤 Téléversement
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-link">
                        🎤 Enregistreur
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-link">
                        📝 Mes Transcriptions
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-link">
                        👤 Mon Compte
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-link">
                        ℹ️ À propos
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-link">
                        📞 Contact
                    </a>
                </li>
                <li>
                    <form class="logout-form">
                        <button type="button" class="logout-button">
                            🚪 Déconnexion
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <div class="content">
        <div class="test-style">
            🔴 TEST : Si vous voyez ce texte en rouge, le CSS est chargé !
        </div>
        
        <h1>Test de la Navbar</h1>
        <p>Cette page teste le style de la navbar avec les modifications appliquées :</p>
        
        <ul>
            <li><strong>justify-content: flex-start !important</strong> - Tout commence à gauche</li>
            <li><strong>gap: 30px !important</strong> - Espacement contrôlé entre logo et pages</li>
            <li><strong>justify-content: flex-start !important</strong> - Pages alignées à gauche</li>
        </ul>
        
        <h2>Instructions :</h2>
        <ol>
            <li>Vérifiez que le logo "🎤 Transcription IA" est à gauche</li>
            <li>Vérifiez que les pages commencent directement après le logo</li>
            <li>Vérifiez qu'il n'y a pas d'espace vide entre le logo et les pages</li>
            <li>Vérifiez que les pages s'alignent de gauche à droite</li>
        </ol>
        
        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h3>✅ Résultat Attendu :</h3>
            <p>La navbar doit afficher :</p>
            <p><strong>[🎤 Transcription IA] [🏠 Accueil] [📤 Téléversement] [🎤 Enregistreur] [📝 Mes Transcriptions] [👤 Mon Compte] [ℹ️ À propos] [📞 Contact] [🚪 Déconnexion]</strong></p>
            <p>Tout aligné à gauche sans espace vide au début.</p>
        </div>
        
        <div style="background: #fff3e0; padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h3>🔧 Si le style ne change pas :</h3>
            <ol>
                <li>Videz le cache du navigateur (Ctrl+F5)</li>
                <li>Ouvrez les outils de développement (F12)</li>
                <li>Allez dans l'onglet "Network" et cochez "Disable cache"</li>
                <li>Rechargez la page</li>
            </ol>
        </div>
    </div>
</body>
</html>
