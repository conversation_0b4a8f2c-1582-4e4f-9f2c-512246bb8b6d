<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💡 3 Idées Principales Simples</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .lightbulb-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }
        
        .success-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #4caf50;
        }
        
        .success-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .idea-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .idea-item::before {
            content: attr(data-number);
            position: absolute;
            left: -15px;
            top: 20px;
            background: #28a745;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .idea-item h4 {
            color: #2e7d32;
            margin-bottom: 15px;
            font-size: 1.2rem;
            margin-left: 25px;
        }
        
        .idea-item p {
            margin-left: 25px;
            color: #495057;
            font-size: 1rem;
        }
        
        .example-box {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0 15px 25px;
            font-style: italic;
            color: #1565c0;
            line-height: 1.5;
        }
        
        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="lightbulb-icon">💡</div>
            <h1 class="title">3 Idées Principales Simples !</h1>
            <p class="subtitle">Exactement 3 idées basées sur les concepts de votre transcription</p>
        </div>

        <!-- Confirmation -->
        <div class="success-section">
            <h3>✅ Solution Simple et Efficace</h3>
            <p><strong>Parfait !</strong> J'ai créé une fonction simple qui génère exactement <span class="highlight">3 idées principales</span> basées sur les vrais concepts extraits de votre texte transcrit.</p>
        </div>

        <!-- Structure des 3 idées -->
        <div class="idea-item" data-number="1">
            <h4>Idée 1 : Sujet Principal</h4>
            <p>Identifie le thème central de votre transcription en utilisant les 2 concepts les plus fréquents.</p>
            <div class="example-box">
                <strong>Format :</strong> "Le texte traite principalement de [concept1] et de [concept2]."<br>
                <strong>Exemple :</strong> "Le texte traite principalement de intelligence artificielle et de apprentissage automatique."
            </div>
        </div>

        <div class="idea-item" data-number="2">
            <h4>Idée 2 : Aspects Développés</h4>
            <p>Présente les thèmes secondaires abordés dans votre contenu.</p>
            <div class="example-box">
                <strong>Format :</strong> "Il développe les aspects de [concept3] et [concept4]."<br>
                <strong>Exemple :</strong> "Il développe les aspects de algorithmes et données."
            </div>
        </div>

        <div class="idea-item" data-number="3">
            <h4>Idée 3 : Conclusion/Implications</h4>
            <p>Synthétise l'importance ou les implications du sujet traité.</p>
            <div class="example-box">
                <strong>Format :</strong> "L'analyse met en évidence l'importance de [concept5] dans ce domaine."<br>
                <strong>Exemple :</strong> "L'analyse met en évidence l'importance de technologie dans ce domaine."
            </div>
        </div>

        <!-- Garanties -->
        <div class="success-section">
            <h3>🎯 Garanties</h3>
            <div style="background: white; border-radius: 15px; padding: 20px; margin: 15px 0; border-left: 4px solid #28a745;">
                <h4 style="color: #2e7d32; margin-bottom: 15px;">✅ Ce qui est garanti :</h4>
                <ul>
                    <li><strong>Exactement 3 idées :</strong> Ni plus, ni moins</li>
                    <li><strong>Concepts de VOTRE texte :</strong> Utilise les vrais mots-clés extraits</li>
                    <li><strong>Phrases courtes :</strong> Idées concises et claires</li>
                    <li><strong>Structure cohérente :</strong> Sujet → Développement → Conclusion</li>
                    <li><strong>Toujours fonctionnel :</strong> Même avec des textes courts</li>
                </ul>
            </div>
        </div>

        <!-- Exemple complet -->
        <div class="success-section">
            <h3>📝 Exemple Complet</h3>
            <p><strong>Si votre transcription parle d'intelligence artificielle :</strong></p>
            
            <div style="background: white; border-radius: 15px; padding: 20px; margin: 15px 0; border-left: 4px solid #2196f3;">
                <div style="color: #1565c0; font-weight: 600; margin-bottom: 10px;">💡 Idées principales générées :</div>
                <ol style="color: #495057; line-height: 1.8;">
                    <li>"Le texte traite principalement de intelligence artificielle et de apprentissage automatique."</li>
                    <li>"Il développe les aspects de algorithmes et données."</li>
                    <li>"L'analyse met en évidence l'importance de technologie dans ce domaine."</li>
                </ol>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/telechargement/" class="btn btn-success">📤 Tester 3 Idées Principales</a>
            <a href="http://127.0.0.1:8000/enregistreur/" class="btn">🎤 Tester avec Audio</a>
        </div>

        <div class="success-section">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Solution simple et parfaite !</strong> Votre application génère maintenant exactement 
                <span class="highlight">3 idées principales courtes</span> qui utilisent les vrais concepts de votre transcription. 
                Chaque idée est <span class="highlight">unique et pertinente</span> à votre contenu spécifique ! 🎉
            </p>
            
            <p style="color: #2e7d32; font-weight: 500; font-size: 1rem; margin: 20px 0 0 0; text-align: center;">
                <strong>Testez maintenant - vous aurez exactement 3 idées principales !</strong>
            </p>
        </div>
    </div>
</body>
</html>
