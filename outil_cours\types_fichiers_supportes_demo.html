<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📁 Types de Fichiers Supportés</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .files-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.8rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.3rem;
            font-weight: 500;
        }
        
        .file-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }
        
        .file-type-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .file-type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .file-type-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .file-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .file-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #2d3748;
        }
        
        .file-description {
            color: #5a6c7d;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .file-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .file-features li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            color: #4a5568;
            font-weight: 500;
        }
        
        .file-features li:last-child {
            border-bottom: none;
        }
        
        .file-features li::before {
            content: '✅';
            margin-right: 10px;
        }
        
        .removed-section {
            background: #fee2e2;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #dc3545;
        }
        
        .removed-section h3 {
            color: #dc3545;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .removed-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .removed-item h4 {
            color: #dc3545;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .summary-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #4caf50;
            text-align: center;
        }
        
        .summary-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .file-types-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="files-icon">📁</div>
            <h1 class="title">Types de Fichiers Supportés</h1>
            <p class="subtitle">Formats optimisés pour une transcription de qualité</p>
        </div>

        <!-- Types de fichiers supportés -->
        <div class="file-types-grid">
            <div class="file-type-card">
                <div class="file-icon">🎬</div>
                <h3 class="file-title">Vidéo MP4</h3>
                <p class="file-description">Format vidéo le plus populaire et compatible</p>
                <ul class="file-features">
                    <li>Extraction audio automatique</li>
                    <li>Transcription de la parole</li>
                    <li>Optimisé pour Google Speech</li>
                    <li>Taille max: 100MB</li>
                </ul>
            </div>
            
            <div class="file-type-card">
                <div class="file-icon">🎵</div>
                <h3 class="file-title">Audio MP3</h3>
                <p class="file-description">Format audio universel pour la transcription</p>
                <ul class="file-features">
                    <li>Transcription directe</li>
                    <li>Conversion automatique</li>
                    <li>Qualité optimisée</li>
                    <li>Traitement rapide</li>
                </ul>
            </div>
            
            <div class="file-type-card">
                <div class="file-icon">📝</div>
                <h3 class="file-title">Texte TXT</h3>
                <p class="file-description">Fichiers texte brut pour analyse directe</p>
                <ul class="file-features">
                    <li>Lecture instantanée</li>
                    <li>Encodage UTF-8</li>
                    <li>Analyse de contenu</li>
                    <li>Génération de résumés</li>
                </ul>
            </div>
            
            <div class="file-type-card">
                <div class="file-icon">📄</div>
                <h3 class="file-title">PDF</h3>
                <p class="file-description">Documents PDF avec extraction de texte</p>
                <ul class="file-features">
                    <li>Extraction de texte</li>
                    <li>Multi-pages</li>
                    <li>Préservation du contenu</li>
                    <li>Analyse complète</li>
                </ul>
            </div>
            
            <div class="file-type-card">
                <div class="file-icon">📘</div>
                <h3 class="file-title">Word DOCX</h3>
                <p class="file-description">Documents Word modernes (2007+)</p>
                <ul class="file-features">
                    <li>Lecture native</li>
                    <li>Formatage préservé</li>
                    <li>Extraction complète</li>
                    <li>Support optimal</li>
                </ul>
            </div>
            
            <div class="file-type-card">
                <div class="file-icon">📗</div>
                <h3 class="file-title">Word DOC</h3>
                <p class="file-description">Documents Word anciens (97-2003)</p>
                <ul class="file-features">
                    <li>Support limité</li>
                    <li>Conversion recommandée</li>
                    <li>Fallback disponible</li>
                    <li>Message informatif</li>
                </ul>
            </div>
        </div>

        <!-- Formats supprimés -->
        <div class="removed-section">
            <h3>❌ Formats Supprimés</h3>
            <p style="color: #dc3545; margin-bottom: 20px;">Ces formats ont été retirés pour simplifier et optimiser l'expérience utilisateur :</p>
            
            <div class="removed-item">
                <h4>WAV, AVI, MOV</h4>
                <p><strong>Raison :</strong> Formats redondants ou moins utilisés</p>
                <p><strong>Alternative :</strong> Convertissez en MP4 (vidéo) ou MP3 (audio)</p>
            </div>
        </div>

        <!-- Résumé -->
        <div class="summary-section">
            <h3>🎯 Résumé des Formats Supportés</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.2rem; margin: 20px 0;">
                <strong>5 formats essentiels :</strong> MP4, MP3, TXT, PDF, DOCX (+ DOC limité)
            </p>
            <p style="color: #2e7d32; margin: 0;">
                ✅ Formats optimisés pour la transcription<br>
                ✅ Compatibilité maximale<br>
                ✅ Traitement rapide et fiable<br>
                ✅ Taille maximum : 100MB
            </p>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/telechargement/" class="btn btn-success">📁 Tester les Formats</a>
            <a href="http://127.0.0.1:8000/" class="btn">🏠 Retour Accueil</a>
        </div>
    </div>
</body>
</html>
