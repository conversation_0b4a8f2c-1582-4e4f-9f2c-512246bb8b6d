<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎵 Solution Format Audio</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .audio-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.8rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.3rem;
            font-weight: 500;
        }
        
        .problem-analysis {
            background: #fee2e2;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #dc3545;
        }
        
        .problem-analysis h3 {
            color: #dc3545;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .solution-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #4caf50;
        }
        
        .solution-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .flow-diagram {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #28a745;
            background: #e8f5e8;
        }
        
        .flow-step.error {
            border-left-color: #dc3545;
            background: #fee2e2;
        }
        
        .step-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            width: 30px;
            text-align: center;
        }
        
        .step-text {
            flex: 1;
            font-weight: 600;
        }
        
        .code-section {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #ff9800;
        }
        
        .code-section h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .format-specs {
            background: #f3e5f5;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #9c27b0;
        }
        
        .format-specs h3 {
            color: #6a1b9a;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .spec-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #9c27b0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .spec-item h4 {
            color: #6a1b9a;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="audio-icon">🎵</div>
            <h1 class="title">Format Audio Corrigé !</h1>
            <p class="subtitle">Solution complète pour la compatibilité avec Google Speech Recognition</p>
        </div>

        <!-- Analyse du problème -->
        <div class="problem-analysis">
            <h3>🔍 Problème Identifié</h3>
            <p><strong>Erreur :</strong> "Erreur de format audio. Le fichier audio n'est pas compatible avec le service de reconnaissance vocale."</p>
            <p><strong>Cause :</strong> MoviePy crée un fichier audio dans un format que Google Speech Recognition n'accepte pas par défaut.</p>
            <p><strong>Impact :</strong> L'extraction audio réussit, mais la transcription échoue à cause du format incompatible.</p>
        </div>

        <!-- Spécifications format -->
        <div class="format-specs">
            <h3>📋 Spécifications Google Speech Recognition</h3>
            
            <div class="spec-item">
                <h4>Format Requis</h4>
                <p><strong>Codec :</strong> PCM 16-bit (pcm_s16le)</p>
                <p><strong>Fréquence :</strong> 16 kHz (16000 Hz)</p>
                <p><strong>Canaux :</strong> Mono (1 canal)</p>
                <p><strong>Format :</strong> WAV non compressé</p>
            </div>
            
            <div class="spec-item">
                <h4>Formats Problématiques</h4>
                <p><strong>MP3 :</strong> Compression avec perte</p>
                <p><strong>AAC :</strong> Format propriétaire</p>
                <p><strong>44.1 kHz :</strong> Fréquence trop élevée</p>
                <p><strong>Stéréo :</strong> Deux canaux non optimaux</p>
            </div>
        </div>

        <!-- Solution appliquée -->
        <div class="solution-section">
            <h3>🔧 Solution Appliquée</h3>
            
            <div class="flow-diagram">
                <h4 style="color: #2e7d32; margin-bottom: 15px;">✅ Nouveau Processus</h4>
                
                <div class="flow-step">
                    <div class="step-icon">1️⃣</div>
                    <div class="step-text">Extraction avec paramètres optimisés (codec, fréquence, mono)</div>
                </div>
                
                <div class="flow-step">
                    <div class="step-icon">2️⃣</div>
                    <div class="step-text">Tentative de transcription avec le fichier optimisé</div>
                </div>
                
                <div class="flow-step">
                    <div class="step-icon">3️⃣</div>
                    <div class="step-text">Si "Bad Request" → Conversion FFmpeg automatique</div>
                </div>
                
                <div class="flow-step">
                    <div class="step-icon">4️⃣</div>
                    <div class="step-text">Nouvelle tentative avec le fichier converti</div>
                </div>
                
                <div class="flow-step">
                    <div class="step-icon">5️⃣</div>
                    <div class="step-text">Nettoyage automatique des fichiers temporaires</div>
                </div>
            </div>
        </div>

        <!-- Code implémenté -->
        <div class="code-section">
            <h3>💻 Code Implémenté</h3>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">1. Extraction avec paramètres optimisés</h4>
            <div class="code-example">
# Extraction avec format compatible Google Speech Recognition
video.audio.write_audiofile(
    audio_path_temp,
    codec='pcm_s16le',  # Format WAV compatible
    ffmpeg_params=['-ar', '16000', '-ac', '1']  # 16kHz mono
)
            </div>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">2. Fonction de conversion de format</h4>
            <div class="code-example">
def convertir_audio_format_compatible(audio_path_input, audio_path_output):
    """Convertit un fichier audio au format compatible avec Google Speech Recognition."""
    cmd = [
        'ffmpeg',
        '-i', audio_path_input,
        '-acodec', 'pcm_s16le',  # Codec WAV 16-bit
        '-ar', '16000',          # Fréquence 16kHz
        '-ac', '1',              # Mono
        '-y',                    # Écraser le fichier de sortie
        audio_path_output
    ]
    # Exécution et vérification...
            </div>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">3. Gestion automatique des erreurs "Bad Request"</h4>
            <div class="code-example">
except sr.RequestError as e:
    if "bad request" in str(e).lower():
        print("❌ Bad Request détecté - tentative de conversion de format...")
        
        # Conversion automatique du format
        audio_converted_path = os.path.join(temp_dir, f"{fichier.id}_audio_converted.wav")
        
        if convertir_audio_format_compatible(wav_path_temp, audio_converted_path):
            print("🔄 Format converti, nouvelle tentative de transcription...")
            contenu_fichier = transcrire_segment_simple(audio_converted_path)
            # Nettoyage automatique du fichier converti
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/telechargement/" class="btn btn-success">🎵 Tester Conversion</a>
            <a href="http://127.0.0.1:8000/" class="btn">🏠 Retour Accueil</a>
        </div>

        <div class="solution-section">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Format audio optimisé</strong> : Le système extrait maintenant l'audio 
                dans le format exact requis par Google Speech Recognition (WAV 16-bit, 16kHz, mono). 
                En cas d'échec, une conversion automatique est effectuée. La transcription devrait 
                maintenant fonctionner parfaitement !
            </p>
        </div>
    </div>
</body>
</html>
