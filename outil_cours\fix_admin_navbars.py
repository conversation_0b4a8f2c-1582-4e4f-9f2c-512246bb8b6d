#!/usr/bin/env python
"""
Script pour ajouter le bouton "Gestionnaire Utilisateurs" dans tous les templates admin
"""

import os
import re

def fix_admin_navbar(file_path):
    """Ajoute le bouton Gestionnaire Utilisateurs dans un template admin"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Chercher le pattern de navbar admin
        pattern = r'(<li><a href="{% url \'admin_transcriptions\' %}" class="nav-link admin">⚙️ Administration</a></li>\s*{% if user\.is_authenticated %})'
        
        # Remplacement avec le nouveau bouton
        replacement = r'<li><a href="{% url \'admin_transcriptions\' %}" class="nav-link admin">⚙️ Administration</a></li>\n                <li><a href="/gestionnaire-utilisateurs/" class="nav-link admin">👥 Gestionnaire Utilisateurs</a></li>\n                {% if user.is_authenticated %}'
        
        # Vérifier si le bouton existe déjà
        if "👥 Gestionnaire Utilisateurs" in content:
            print(f"✅ {file_path} - Bouton déjà présent")
            return True
        
        # Effectuer le remplacement
        new_content = re.sub(pattern, replacement, content)
        
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"✅ {file_path} - Bouton ajouté")
            return True
        else:
            print(f"❌ {file_path} - Pattern non trouvé")
            return False
            
    except Exception as e:
        print(f"❌ {file_path} - Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 Ajout du bouton Gestionnaire Utilisateurs dans les templates admin...")
    
    # Liste des templates admin à modifier
    templates_admin = [
        "templates/transcription_resume/admin_supprimer_transcription.html",
        "templates/transcription_resume/admin_modifier_resume.html", 
        "templates/transcription_resume/admin_supprimer_resume.html",
        "templates/transcription_resume/admin_modifier_analyse.html",
        "templates/transcription_resume/admin_supprimer_analyse.html"
    ]
    
    success_count = 0
    total_count = len(templates_admin)
    
    for template in templates_admin:
        if os.path.exists(template):
            if fix_admin_navbar(template):
                success_count += 1
        else:
            print(f"❌ {template} - Fichier non trouvé")
    
    print(f"\n🎯 Résultat: {success_count}/{total_count} templates modifiés")
    
    if success_count == total_count:
        print("✅ Tous les templates admin ont été mis à jour !")
        print("\n🚀 Maintenant:")
        print("1. Redémarrez le serveur Django")
        print("2. Connectez-vous en admin")
        print("3. Le bouton '👥 Gestionnaire Utilisateurs' devrait apparaître dans toutes les pages admin")
    else:
        print("⚠️ Certains templates n'ont pas pu être modifiés")

if __name__ == "__main__":
    main()
