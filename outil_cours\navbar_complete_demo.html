<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 Navbar Complète</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .navbar-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.8rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.3rem;
            font-weight: 500;
        }
        
        .navbar-demo {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 50%, rgba(240, 147, 251, 0.95) 100%);
            border-radius: 15px;
            padding: 15px 30px;
            margin: 30px 0;
            color: white;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 30px auto;
        }
        
        .brand {
            font-size: 1.5rem;
            font-weight: 900;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .nav-links {
            display: flex;
            gap: 6px;
            list-style: none;
            margin: 0;
            padding: 0;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .nav-links li {
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            font-size: 0.9rem;
            white-space: nowrap;
        }
        
        .nav-links .logout {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }
        
        .pages-list {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #4caf50;
        }
        
        .pages-list h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .page-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .page-item h4 {
            color: #2e7d32;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .page-item .icon {
            font-size: 1.5rem;
            margin-right: 8px;
        }
        
        .improvements-section {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #ff9800;
        }
        
        .improvements-section h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .improvement-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .improvement-item h4 {
            color: #e65100;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .navbar-demo {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .nav-links {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="navbar-icon">📋</div>
            <h1 class="title">Navbar Complète !</h1>
            <p class="subtitle">Toutes les pages affichées avec "Transcription IA" à gauche</p>
        </div>

        <!-- Démonstration de la navbar -->
        <div class="navbar-demo">
            <div class="brand">
                <span>🎤</span>
                Transcription IA
            </div>
            <ul class="nav-links">
                <li>🏠 Accueil</li>
                <li>📤 Téléversement</li>
                <li>🎤 Enregistreur</li>
                <li>📝 Mes Transcriptions</li>
                <li>💬 Consultation</li>
                <li>👤 Mon Compte</li>
                <li>ℹ️ À propos</li>
                <li>📞 Contact</li>
                <li class="logout">🚪 Déconnexion</li>
            </ul>
        </div>

        <!-- Liste complète des pages -->
        <div class="pages-list">
            <h3>📋 Toutes les Pages Affichées dans la Navbar</h3>
            
            <div class="pages-grid">
                <div class="page-item">
                    <h4><span class="icon">🏠</span>Accueil</h4>
                    <p>Page d'accueil principale de l'application</p>
                </div>
                
                <div class="page-item">
                    <h4><span class="icon">📤</span>Téléversement</h4>
                    <p>Upload de fichiers audio/vidéo pour transcription</p>
                </div>
                
                <div class="page-item">
                    <h4><span class="icon">🎤</span>Enregistreur Audio</h4>
                    <p>Enregistrement direct depuis le navigateur</p>
                </div>
                
                <div class="page-item">
                    <h4><span class="icon">📝</span>Mes Transcriptions</h4>
                    <p>Historique de toutes les transcriptions</p>
                </div>
                
                <div class="page-item">
                    <h4><span class="icon">💬</span>Consultation</h4>
                    <p>Page de consultation et analyse de texte</p>
                </div>
                
                <div class="page-item">
                    <h4><span class="icon">👤</span>Mon Compte</h4>
                    <p>Gestion du profil utilisateur</p>
                </div>
                
                <div class="page-item">
                    <h4><span class="icon">ℹ️</span>À propos</h4>
                    <p>Informations sur l'application</p>
                </div>
                
                <div class="page-item">
                    <h4><span class="icon">📞</span>Contact</h4>
                    <p>Page de contact et support</p>
                </div>
            </div>
        </div>

        <!-- Améliorations apportées -->
        <div class="improvements-section">
            <h3>🔧 Améliorations Apportées</h3>
            
            <div class="improvement-item">
                <h4>✅ Page Consultation Ajoutée</h4>
                <p>Ajout de la page "💬 Consultation" dans la navbar pour les utilisateurs connectés</p>
            </div>
            
            <div class="improvement-item">
                <h4>✅ Espacement Optimisé</h4>
                <p>Réduction des gaps (6px) et padding (8px 12px) pour plus de place</p>
            </div>
            
            <div class="improvement-item">
                <h4>✅ Taille de Police Ajustée</h4>
                <p>Font-size réduit à 0.9rem pour un meilleur affichage</p>
            </div>
            
            <div class="improvement-item">
                <h4>✅ Flex Layout Amélioré</h4>
                <p>Ajout de flex: 1 pour que les liens prennent tout l'espace disponible</p>
            </div>
            
            <div class="improvement-item">
                <h4>✅ Responsive Design</h4>
                <p>Adaptation automatique sur mobile avec menu déroulant</p>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/" class="btn btn-success">🏠 Voir Navbar Complète</a>
            <a href="http://127.0.0.1:8000/login/" class="btn">🔑 Se Connecter</a>
        </div>

        <div class="pages-list">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Navbar complète et optimisée</strong> : "🎤 Transcription IA" est maintenant 
                à gauche, et TOUTES les pages (Accueil, Téléversement, Enregistreur, Mes Transcriptions, 
                Consultation, Mon Compte, À propos, Contact, Déconnexion) sont affichées à droite avec 
                un espacement optimisé pour un affichage parfait ! 🎉
            </p>
        </div>
    </div>
</body>
</html>
