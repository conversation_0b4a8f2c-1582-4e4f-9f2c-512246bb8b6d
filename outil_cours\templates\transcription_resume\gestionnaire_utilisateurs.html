<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👥 Gestionnaire des Utilisateurs - Administration</title>
    <meta name="csrf-token" content="{{ csrf_token }}">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            line-height: 1.6;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Navbar */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
            align-items: center;
        }

        .nav-link {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 600;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateY(-2px);
        }

        .nav-link.admin {
            background: linear-gradient(135deg, #f44336, #e91e63);
            color: white;
            padding: 10px 20px;
        }

        .nav-link.admin:hover {
            background: linear-gradient(135deg, #d32f2f, #c2185b);
            transform: translateY(-2px) scale(1.05);
        }

        /* Container principal */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .admin-header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .admin-title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .admin-subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }

        .admin-info {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 15px;
            padding: 15px 20px;
            margin-top: 20px;
            border-left: 4px solid #2196f3;
        }

        .admin-info p {
            margin: 0;
            color: #1565c0;
            font-size: 0.95rem;
            font-weight: 500;
        }

        /* Statistiques globales */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #5a6c7d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        /* Section utilisateurs */
        .users-section {
            margin-bottom: 40px;
        }

        .user-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .user-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .user-info h3 {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .user-info p {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .user-stats {
            display: flex;
            gap: 20px;
        }

        .user-stat {
            text-align: center;
        }

        .user-stat-number {
            font-size: 1.5rem;
            font-weight: 900;
        }

        .user-stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .user-content {
            padding: 30px;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .users-table th,
        .users-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .users-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .users-table tr:hover {
            background: #f8fafc;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .user-details h4 {
            margin: 0;
            color: #1e293b;
            font-weight: 600;
        }

        .user-details p {
            margin: 0;
            color: #64748b;
            font-size: 0.9rem;
        }

        .stats-cell {
            text-align: center;
        }

        .stat-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            margin: 2px;
        }

        .stat-transcriptions {
            background: #dbeafe;
            color: #1e40af;
        }

        .stat-resumes {
            background: #dcfce7;
            color: #166534;
        }

        .stat-fichiers {
            background: #fef3c7;
            color: #92400e;
        }

        .stat-analyses {
            background: #fce7f3;
            color: #be185d;
        }

        .actions-cell {
            text-align: center;
        }

        .btn-delete {
            padding: 8px 16px;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            text-decoration: none;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #64748b;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .date-cell {
            color: #64748b;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }

            .title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }

            .nav-btn {
                width: 100%;
                max-width: 300px;
                text-align: center;
            }

            .users-table {
                font-size: 0.9rem;
            }

            .users-table th,
            .users-table td {
                padding: 10px 8px;
            }
        }
        /* Boutons d'action */
        .btn {
            padding: 6px 12px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-delete {
            background: linear-gradient(135deg, #f44336, #e91e63);
            color: white;
        }

        .btn-delete:hover {
            background: linear-gradient(135deg, #d32f2f, #c2185b);
            transform: translateY(-1px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                gap: 15px;
            }

            .nav-link {
                padding: 6px 12px;
                font-size: 0.9rem;
            }

            .container {
                padding: 20px 15px;
            }

            .admin-header {
                padding: 25px;
            }

            .admin-title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .user-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .user-stats {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="{% url 'accueil' %}" class="nav-brand">🎤 Transcription IA</a>
            <ul class="nav-links">
                <li><a href="{% url 'admin_transcriptions' %}" class="nav-link admin">⚙️ Administration</a></li>
                <li><a href="/gestionnaire-utilisateurs/" class="nav-link admin">👥 Gestionnaire Utilisateurs</a></li>
                {% if user.is_authenticated %}
                    <li>
                        <form method="post" action="{% url 'logout' %}" style="display: inline;">
                            {% csrf_token %}
                            <button type="submit" class="nav-link" style="background: none; border: none; cursor: pointer; color: inherit; font: inherit;">
                                🚪 Déconnexion
                            </button>
                        </form>
                    </li>
                {% else %}
                    <li><a href="{% url 'login' %}" class="nav-link">🔑 Connexion</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <div class="container">
        <!-- En-tête d'administration -->
        <div class="admin-header">
            <h1 class="admin-title">👥 Gestionnaire des Utilisateurs</h1>
            <p class="admin-subtitle">Administration et gestion des comptes utilisateurs</p>
            <div class="admin-info">
                <p><strong>ℹ️ Note :</strong> Interface de gestion pour supprimer les comptes utilisateurs et consulter leurs statistiques d'activité.</p>
            </div>
        </div>

        <!-- Statistiques globales -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ stats_globales.total_utilisateurs }}</div>
                <div class="stat-label">👤 Utilisateurs Actifs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats_globales.total_transcriptions }}</div>
                <div class="stat-label">📝 Transcriptions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats_globales.total_resumes }}</div>
                <div class="stat-label">📋 Résumés</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats_globales.total_fichiers }}</div>
                <div class="stat-label">📁 Fichiers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats_globales.total_analyses }}</div>
                <div class="stat-label">🔍 Analyses</div>
            </div>
        </div>

        <!-- Section des utilisateurs -->
        <div class="users-section">
            {% if utilisateurs %}
                {% for utilisateur in utilisateurs %}
                    <div class="user-card">
                        <div class="user-header">
                            <div class="user-info">
                                <h3>👤
                                    {% if utilisateur.first_name or utilisateur.last_name %}
                                        {{ utilisateur.first_name }} {{ utilisateur.last_name }}
                                    {% else %}
                                        {{ utilisateur.username }}
                                    {% endif %}
                                </h3>
                                <p>📧 {{ utilisateur.email|default:"Email non renseigné" }} • 📅 Inscrit le {{ utilisateur.date_joined|date:"d/m/Y" }}</p>
                            </div>
                            <div class="user-stats">
                                <div class="user-stat">
                                    <div class="user-stat-number">{{ utilisateur.nb_transcriptions }}</div>
                                    <div class="user-stat-label">Transcriptions</div>
                                </div>
                                <div class="user-stat">
                                    <div class="user-stat-number">{{ utilisateur.nb_resumes }}</div>
                                    <div class="user-stat-label">Résumés</div>
                                </div>
                                <div class="user-stat">
                                    <div class="user-stat-number">{{ utilisateur.nb_fichiers }}</div>
                                    <div class="user-stat-label">Fichiers</div>
                                </div>
                                <div class="user-stat">
                                    <div class="user-stat-number">{{ utilisateur.nb_analyses }}</div>
                                    <div class="user-stat-label">Analyses</div>
                                </div>
                            </div>
                        </div>
                        <div class="user-content">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <p><strong>Activité :</strong>
                                        {% if utilisateur.nb_transcriptions == 0 and utilisateur.nb_resumes == 0 and utilisateur.nb_fichiers == 0 and utilisateur.nb_analyses == 0 %}
                                            <span style="color: #9ca3af; font-style: italic;">Aucune activité</span>
                                        {% else %}
                                            <span style="color: #10b981;">Utilisateur actif</span>
                                        {% endif %}
                                    </p>
                                    <p><strong>Dernière connexion :</strong> {{ utilisateur.last_login|date:"d/m/Y H:i"|default:"Jamais connecté" }}</p>
                                </div>
                                <div>
                                    <a href="{% url 'supprimer_utilisateur' utilisateur.id %}"
                                       class="btn btn-delete"
                                       onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')">
                                        🗑️ Supprimer
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">👥</div>
                    <h3>Aucun utilisateur trouvé</h3>
                    <p>Il n'y a actuellement aucun utilisateur dans le système.</p>
                </div>
            {% endif %}
        </div>
    </div>


</body>
</html>
