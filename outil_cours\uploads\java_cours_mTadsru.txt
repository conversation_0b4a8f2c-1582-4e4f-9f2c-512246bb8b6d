Excellent ! Java est un autre langage de programmation extrêmement puissant et très demandé. Il est la base de beaucoup d'applications d'entreprise, d'applications Android et de systèmes à grande échelle.

Voici un cours sur les bases de Java. Il est important de noter que Java est un langage "fortement typé" et "orienté objet" dès le départ, ce qui le rend un peu plus structuré que Python.

Cours de Java : Les Bases
1. Introduction à Java
Qu'est-ce que Java ? Java est un langage de programmation orienté objet, de haut niveau, développé par Sun Microsystems (maintenant Oracle). Il est conçu pour être indépendant de la plateforme ("Write Once, Run Anywhere" - <PERSON><PERSON><PERSON><PERSON> une fois, exécuter partout).
Pourquoi apprendre Java ?
Portabilité : Les programmes Java compilés (en bytecode) peuvent s'exécuter sur n'importe quel appareil doté d'une Machine Virtuelle Java (JVM), qu'il s'agisse de Windows, macOS, Linux, ou Android.
Orienté Objet : Favorise une conception modulaire et réutilisable du code.
Performance : Bien que non aussi rapide que le C++, Java est très performant grâce à la JVM et à son compilateur JIT (Just-In-Time).
Sécurité : Des fonctionnalités de sécurité intégrées le rendent adapté aux applications réseau.
Grande Communauté & Écosystème : Une immense communauté de développeurs, de nombreux frameworks (Spring, Hibernate), bibliothèques et outils.
JVM, JRE, JDK : C'est quoi ?
JVM (Java Virtual Machine) : La machine virtuelle qui exécute le bytecode Java. C'est elle qui assure la portabilité.
JRE (Java Runtime Environment) : Contient la JVM et les bibliothèques Java nécessaires pour exécuter des applications Java.
JDK (Java Development Kit) : Contient le JRE plus les outils de développement (comme le compilateur javac et l'exécuteur java). Vous avez besoin du JDK pour développer des applications Java.
2. Votre Premier Programme : "Hello, World!"
En Java, tout le code doit se trouver à l'intérieur de classes, et le point de départ d'un programme est la méthode main.

Java

// Ceci est un commentaire sur une seule ligne.
/*
Ceci est un commentaire
sur plusieurs lignes.
*/

// Chaque programme Java commence par une classe.
public class HelloWorld {

    // La méthode main est le point d'entrée de votre programme.
    // Elle doit toujours être définie de cette manière.
    public static void main(String[] args) {
        // System.out.println() est utilisé pour afficher du texte à la console.
        System.out.println("Hello, World!"); // N'oubliez pas le point-virgule à la fin !
    }
}
Pour compiler et exécuter ce code :

Enregistrez-le sous le nom HelloWorld.java.
Ouvrez un terminal/invite de commande et naviguez jusqu'au répertoire où vous avez enregistré le fichier.
Compilez : javac HelloWorld.java (cela crée un fichier HelloWorld.class).
Exécutez : java HelloWorld (cela exécute le bytecode via la JVM).
3. Les Variables et Types de Données
En Java, vous devez déclarer le type d'une variable avant de pouvoir l'utiliser.

Java

public class VariablesDemo {
    public static void main(String[] args) {
        // Types de données primitifs :
        int age = 30;         // Entier
        double prix = 19.99;  // Nombre à virgule flottante (décimal)
        boolean estValide = true; // Booléen (true ou false)
        char initiale = 'J';  // Caractère unique (entre guillemets simples)

        // Type de données non-primitif (objet) : String
        String nom = "Alice"; // Chaîne de caractères (entre guillemets doubles)

        System.out.println("Nom : " + nom);
        System.out.println("Âge : " + age);
        System.out.println("Prix : " + prix);
        System.out.println("Est valide : " + estValide);
        System.out.println("Initiale : " + initiale);

        // Les variables peuvent changer de valeur (si elles ne sont pas finales)
        age = 31;
        System.out.println("Nouvel âge : " + age);

        // Déclaration sans initialisation immédiate
        int quantite;
        quantite = 100;
        System.out.println("Quantité : " + quantite);
    }
}
Types de Données Primitifs Courants :

byte, short, int, long : Nombres entiers de différentes tailles. int est le plus couramment utilisé.
float, double : Nombres à virgule flottante. double offre une meilleure précision.
boolean : true ou false.
char : Un seul caractère.
Type de Données Non-Primitif (Objet) :

String : Représente une séquence de caractères (du texte). C'est une classe en Java, pas un type primitif.
4. Les Opérateurs
Les opérateurs en Java sont similaires à ceux de Python et d'autres langages, mais avec quelques spécificités.

4.1 Opérateurs Arithmétiques
Opérateur	Description	Exemple	Résultat
+	Addition	5 + 3	8
-	Soustraction	10 - 4	6
*	Multiplication	7 * 2	14
/	Division	10 / 3	3 (division entière si les deux opérandes sont int)
%	Modulo (reste)	10 % 3	1

Exporter vers Sheets
Java

public class OperatorsDemo {
    public static void main(String[] args) {
        int a = 10;
        int b = 3;
        System.out.println("a + b = " + (a + b)); // 13
        System.out.println("a / b (int) = " + (a / b)); // 3 (division entière)
        System.out.println("a % b = " + (a % b)); // 1

        double x = 10.0;
        double y = 3.0;
        System.out.println("x / y (double) = " + (x / y)); // 3.333...

        // Incrémentation et décrémentation
        a++; // a devient 11 (équivalent à a = a + 1;)
        System.out.println("a après a++ : " + a);
        b--; // b devient 2 (équivalent à b = b - 1;)
        System.out.println("b après b-- : " + b);
    }
}
4.2 Opérateurs de Comparaison
Ils retournent une valeur boolean (true ou false).

Opérateur	Description	Exemple	Résultat
==	Égal à	5 == 5	true
!=	Différent de	5 != 10	true
<	Inférieur à	5 < 10	true
>	Supérieur à	10 > 5	true
<=	Inférieur ou égal à	5 <= 5	true
>=	Supérieur ou égal à	10 >= 5	true

Exporter vers Sheets
4.3 Opérateurs Logiques
Utilisés pour combiner des expressions booléennes.

&& (AND logique) : true si les deux expressions sont true.
|| (OR logique) : true si au moins une des expressions est true.
! (NOT logique) : Inverse la valeur booléenne (true devient false, false devient true).
Java

public class LogicOperators {
    public static void main(String[] args) {
        boolean a = true;
        boolean b = false;
        System.out.println("a && b : " + (a && b)); // false
        System.out.println("a || b : " + (a || b)); // true
        System.out.println("!a : " + (!a));         // false
    }
}
5. L'Entrée Utilisateur (Scanner)
Pour lire l'entrée de l'utilisateur à partir de la console, vous utilisez la classe Scanner (qui fait partie du package java.util).

Java

import java.util.Scanner; // Importe la classe Scanner

public class UserInput {
    public static void main(String[] args) {
        // Crée un objet Scanner pour lire l'entrée de la console
        Scanner scanner = new Scanner(System.in);

        System.out.print("Entrez votre nom : ");
        String nom = scanner.nextLine(); // Lit une ligne de texte
        System.out.println("Bonjour, " + nom + "!");

        System.out.print("Quel est votre âge ? ");
        int age = scanner.nextInt(); // Lit un entier
        System.out.println("Vous avez " + age + " ans.");

        // IMPORTANT : après nextInt(), nextDouble(), etc.,
        // il faut parfois un scanner.nextLine() pour consommer le reste de la ligne
        // (le caractère Entrée) avant de lire une nouvelle ligne de texte.
        scanner.nextLine(); // Consomme le retour à la ligne après nextInt()

        System.out.print("Quelle est votre ville préférée ? ");
        String ville = scanner.nextLine();
        System.out.println("Votre ville préférée est " + ville + ".");


        scanner.close(); // Il est bonne pratique de fermer le scanner quand vous avez fini
    }
}
Note: L'importation de java.util.Scanner; est nécessaire pour pouvoir utiliser la classe Scanner.

6. Les Conditions (if, else if, else)
Les conditions permettent à votre programme de prendre des décisions.

Java

public class ConditionsDemo {
    public static void main(String[] args) {
        int score = 75;

        if (score >= 90) {
            System.out.println("Excellent !");
        } else if (score >= 70) { // Si la première condition est fausse, on teste celle-ci
            System.out.println("Bien joué !");
        } else if (score >= 50) {
            System.out.println("Passable.");
        } else { // Si toutes les conditions précédentes sont fausses
            System.out.println("À améliorer.");
        }

        String couleur = "rouge";
        if (couleur.equals("rouge")) { // Pour comparer des Strings, utilisez .equals()
            System.out.println("C'est une voiture rouge.");
        } else {
            System.out.println("Ce n'est pas une voiture rouge.");
        }
    }
}
Attention : Utilisez == pour comparer des types primitifs (int, boolean, char, double). Pour comparer des objets comme les String, utilisez la méthode .equals().

7. Les Boucles (for, while, do-while)
Les boucles sont utilisées pour exécuter un bloc de code de manière répétée.

7.1 Boucle for
Idéale lorsque vous savez combien de fois vous voulez répéter une action.

Java

public class ForLoopDemo {
    public static void main(String[] args) {
        // Boucle for : (initialisation; condition; incrémentation/décrémentation)
        for (int i = 0; i < 5; i++) {
            System.out.println("Compteur : " + i);
        }

        System.out.println("---");

        // Parcourir un tableau (voir section suivante sur les tableaux)
        String[] fruits = {"pomme", "banane", "cerise"};
        for (int i = 0; i < fruits.length; i++) {
            System.out.println("J'aime les " + fruits[i]);
        }

        System.out.println("---");

        // Boucle for-each (plus simple pour parcourir des collections)
        for (String fruit : fruits) {
            System.out.println("Fruit : " + fruit);
        }
    }
}
7.2 Boucle while
S'exécute tant qu'une condition est true.

Java

public class WhileLoopDemo {
    public static void main(String[] args) {
        int compteur = 0;
        while (compteur < 5) {
            System.out.println("Compteur while : " + compteur);
            compteur++; // N'oubliez pas d'incrémenter pour éviter une boucle infinie !
        }
    }
}
7.3 Boucle do-while
Similaire à while, mais le bloc de code est exécuté au moins une fois, avant que la condition ne soit testée.

Java

public class DoWhileLoopDemo {
    public static void main(String[] args) {
        int x = 0;
        do {
            System.out.println("X est : " + x);
            x++;
        } while (x < 0); // La condition est fausse, mais le code s'exécute une fois
    }
}
8. Les Tableaux (Arrays)
Un tableau est une structure de données qui stocke une collection d'éléments du même type dans un ordre séquentiel. Sa taille est fixe une fois créé.

Java

public class ArraysDemo {
    public static void main(String[] args) {
        // Déclaration et initialisation d'un tableau d'entiers
        int[] nombres = {10, 20, 30, 40, 50};

        // Accéder aux éléments (l'indexation commence à 0)
        System.out.println("Premier nombre : " + nombres[0]); // Affiche 10
        System.out.println("Troisième nombre : " + nombres[2]); // Affiche 30

        // Modifier un élément
        nombres[1] = 25;
        System.out.println("Deuxième nombre modifié : " + nombres[1]); // Affiche 25

        // Longueur du tableau
        System.out.println("Nombre d'éléments dans le tableau : " + nombres.length);

        // Déclaration d'un tableau avec une taille, puis remplissage
        String[] noms = new String[3]; // Un tableau de 3 Strings
        noms[0] = "Alice";
        noms[1] = "Bob";
        noms[2] = "Charlie";

        for (int i = 0; i < noms.length; i++) {
            System.out.println("Nom à l'index " + i + " : " + noms[i]);
        }
    }
}
9. Les Méthodes (Fonctions en Java)
Les méthodes sont des blocs de code qui exécutent une tâche spécifique. Elles aident à organiser le code et à le rendre réutilisable.

Java

public class MethodsDemo {

    // Une méthode simple qui ne retourne rien (void) et ne prend pas de paramètre
    public static void saluer() {
        System.out.println("Bonjour tout le monde !");
    }

    // Une méthode qui prend un paramètre (String nom) et ne retourne rien
    public static void saluerPersonne(String nom) {
        System.out.println("Bonjour, " + nom + "!");
    }

    // Une méthode qui prend deux paramètres (int a, int b) et retourne un int
    public static int additionner(int a, int b) {
        int resultat = a + b;
        return resultat; // Retourne la valeur du résultat
    }

    // Une méthode qui vérifie si un nombre est pair et retourne un boolean
    public static boolean estPair(int nombre) {
        if (nombre % 2 == 0) {
            return true;
        } else {
            return false;
        }
    }

    public static void main(String[] args) {
        // Appeler les méthodes
        saluer(); // Appelle la première méthode

        saluerPersonne("Alice"); // Appelle la deuxième méthode avec "Alice" comme argument
        saluerPersonne("Bob");

        int somme = additionner(5, 3); // Appelle la troisième méthode et stocke le résultat
        System.out.println("La somme est : " + somme); // Affiche 8

        System.out.println("4 est pair ? " + estPair(4)); // Affiche true
        System.out.println("7 est pair ? " + estPair(7)); // Affiche false
    }
}
Note sur public static : Pour l'instant, comprenez que public static est nécessaire pour pouvoir appeler une méthode directement depuis la méthode main sans avoir à créer un objet de la classe. Ces concepts seront approfondis dans les cours sur la Programmation Orientée Objet (POO).

10. Concepts de Base de la Programmation Orientée Objet (POO)
Java est un langage orienté objet. Voici une très brève introduction :

Classe : Un plan ou un modèle pour créer des objets. Elle définit les attributs (variables) et les comportements (méthodes) que les objets de ce type auront.
Objet : Une instance concrète d'une classe. C'est l'entité réelle qui existe en mémoire.
Java

// Définition d'une classe simple 'Chien'
class Chien {
    // Attributs (variables de l'objet)
    String nom;
    String race;
    int age;

    // Constructeur : méthode spéciale pour créer des objets de cette classe
    public Chien(String nomChien, String raceChien, int ageChien) {
        this.nom = nomChien;  // 'this' fait référence à l'attribut de l'objet courant
        this.race = raceChien;
        this.age = ageChien;
    }

    // Méthode (comportement de l'objet)
    public void aboyer() {
        System.out.println(nom + " aboie : Wouaf ! Wouaf !");
    }

    public void afficherInfos() {
        System.out.println("Nom : " + nom + ", Race : " + race + ", Âge : " + age + " ans.");
    }
}

public class POODemo {
    public static void main(String[] args) {
        // Créer des objets (instances) de la classe Chien
        Chien monChien = new Chien("Rex", "Berger Allemand", 3);
        Chien autreChien = new Chien("Buddy", "Labrador", 5);

        // Accéder aux attributs et appeler les méthodes des objets
        monChien.afficherInfos();
        monChien.aboyer();

        autreChien.afficherInfos();
        autreChien.aboyer();
    }
}