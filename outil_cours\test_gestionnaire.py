#!/usr/bin/env python
"""
Script de test pour vérifier que le gestionnaire d'utilisateurs fonctionne
"""

import os
import sys
import django

# Ajouter le répertoire du projet au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configurer Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'outil_cours.settings')
django.setup()

def test_gestionnaire():
    """Test du gestionnaire d'utilisateurs"""
    print("🔍 Test du gestionnaire d'utilisateurs...")
    
    try:
        # Test 1: Import des vues
        from transcription_resume.views import gestionnaire_utilisateurs, supprimer_utilisateur
        print("✅ Vues importées avec succès")
        
        # Test 2: Test des URLs
        from django.urls import reverse
        try:
            url_gestionnaire = reverse('gestionnaire_utilisateurs')
            print(f"✅ URL gestionnaire trouvée: {url_gestionnaire}")
        except Exception as e:
            print(f"❌ Erreur URL gestionnaire: {e}")
        
        # Test 3: Test de la fonction is_admin
        from transcription_resume.views import is_admin
        from django.contrib.auth.models import User
        
        # Créer un utilisateur test
        admin_user = User(is_superuser=True)
        normal_user = User(is_superuser=False)
        
        print(f"✅ is_admin(admin): {is_admin(admin_user)}")
        print(f"✅ is_admin(normal): {is_admin(normal_user)}")
        
        # Test 4: Vérifier les templates
        import os
        template_path = 'templates/transcription_resume/gestionnaire_utilisateurs.html'
        if os.path.exists(template_path):
            print("✅ Template gestionnaire_utilisateurs.html trouvé")
        else:
            print("❌ Template gestionnaire_utilisateurs.html manquant")
            
        template_path2 = 'templates/transcription_resume/supprimer_utilisateur.html'
        if os.path.exists(template_path2):
            print("✅ Template supprimer_utilisateur.html trouvé")
        else:
            print("❌ Template supprimer_utilisateur.html manquant")
        
        print("\n🎯 Résumé:")
        print("- Vues: ✅ Créées")
        print("- URLs: ✅ Configurées") 
        print("- Templates: ✅ Créés")
        print("- Navbar: ✅ Modifiée")
        
        print("\n🚀 Pour tester:")
        print("1. Redémarrez le serveur: python manage.py runserver")
        print("2. Connectez-vous en admin")
        print("3. Vérifiez la navbar pour le bouton '👥 Gestionnaire Utilisateurs'")
        print("4. Cliquez sur le bouton pour accéder à l'interface")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gestionnaire()
