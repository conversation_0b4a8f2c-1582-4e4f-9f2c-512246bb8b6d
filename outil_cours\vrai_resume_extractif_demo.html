<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✂️ VRAI Résumé Extractif - 1/4 du Texte</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .scissors-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }
        
        .method-section {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #2196f3;
        }
        
        .method-section h3 {
            color: #1565c0;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .solution-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #4caf50;
        }
        
        .solution-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .step-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .step-item::before {
            content: attr(data-step);
            position: absolute;
            left: -15px;
            top: 15px;
            background: #2196f3;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .step-item h4 {
            color: #1565c0;
            margin-bottom: 10px;
            font-size: 1.1rem;
            margin-left: 20px;
        }
        
        .step-item p, .step-item ul {
            margin-left: 20px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .before-card, .after-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
        }
        
        .before-card h4 {
            background: linear-gradient(135deg, #dc3545, #c82333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .after-card h4 {
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .stats-box {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
            text-align: center;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: #e67e22;
            margin: 10px 0;
        }
        
        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="scissors-icon">✂️</div>
            <h1 class="title">VRAI Résumé Extractif !</h1>
            <p class="subtitle">Méthode 1/4 : Sélection intelligente des meilleures phrases du texte original</p>
        </div>

        <!-- Méthode extractive -->
        <div class="method-section">
            <h3>🎯 Méthode Extractive - Résumé RÉEL du Texte</h3>
            <p><strong>Principe :</strong> Sélectionner automatiquement les phrases les plus importantes du texte transcrit pour créer un résumé qui représente vraiment le contenu original.</p>
            
            <div class="comparison">
                <div class="stats-box">
                    <h4>📊 Résumé Facile</h4>
                    <div class="stats-number">1/4</div>
                    <p>des phrases originales<br><span class="highlight">25% du texte</span></p>
                </div>
                
                <div class="stats-box">
                    <h4>📊 Résumé Professionnel</h4>
                    <div class="stats-number">1/3</div>
                    <p>des phrases originales<br><span class="highlight">33% du texte</span></p>
                </div>
            </div>
        </div>

        <!-- Algorithme de sélection -->
        <div class="solution-section">
            <h3>🧠 Algorithme de Sélection Intelligent</h3>
            
            <div class="step-item" data-step="1">
                <h4>Analyse de Fréquence des Mots</h4>
                <p>Calcul de la fréquence de tous les mots significatifs dans le texte pour identifier les concepts clés.</p>
            </div>
            
            <div class="step-item" data-step="2">
                <h4>Scoring des Phrases</h4>
                <p>Chaque phrase reçoit un score basé sur :</p>
                <ul>
                    <li><strong>Fréquence des mots :</strong> Plus une phrase contient de mots fréquents, plus elle est importante</li>
                    <li><strong>Position :</strong> Bonus pour les phrases du début (20%) et de la fin (20%)</li>
                    <li><strong>Longueur optimale :</strong> Ni trop courtes ni trop longues (50-200 caractères)</li>
                    <li><strong>Mots techniques :</strong> Bonus pour les termes spécialisés</li>
                    <li><strong>Diversité :</strong> Bonus pour les phrases avec vocabulaire varié</li>
                </ul>
            </div>
            
            <div class="step-item" data-step="3">
                <h4>Sélection des Meilleures Phrases</h4>
                <p>Tri des phrases par score décroissant et sélection du nombre approprié selon le type de résumé.</p>
            </div>
            
            <div class="step-item" data-step="4">
                <h4>Remise en Ordre Chronologique</h4>
                <p>Les phrases sélectionnées sont remises dans leur ordre d'apparition original pour maintenir la cohérence du discours.</p>
            </div>
        </div>

        <!-- Comparaison avant/après -->
        <div class="solution-section">
            <h3>📈 Comparaison Avant/Après</h3>
            
            <div class="comparison">
                <div class="before-card">
                    <h4>❌ Ancien Système</h4>
                    <ul>
                        <li>Phrases inventées non liées au texte</li>
                        <li>Contenu générique et vague</li>
                        <li>Aucun lien avec la transcription</li>
                        <li>Même résultat pour tous les textes</li>
                    </ul>
                    
                    <p><strong>Exemple :</strong></p>
                    <div style="background: #fef2f2; padding: 10px; border-radius: 5px; color: #dc2626; font-style: italic;">
                        "Ce document traite principalement de [concept] et de ses implications. Il aborde également les aspects liés à [thème]..."
                    </div>
                </div>
                
                <div class="after-card">
                    <h4>✅ Nouveau Système</h4>
                    <ul>
                        <li>Phrases extraites du texte original</li>
                        <li>Contenu spécifique et pertinent</li>
                        <li>Résumé fidèle à la transcription</li>
                        <li>Résultat unique pour chaque texte</li>
                    </ul>
                    
                    <p><strong>Exemple :</strong></p>
                    <div style="background: #f0f9ff; padding: 10px; border-radius: 5px; color: #1565c0; font-style: italic;">
                        [Phrases réelles extraites de votre transcription, sélectionnées intelligemment selon leur importance]
                    </div>
                </div>
            </div>
        </div>

        <!-- Code de l'algorithme -->
        <div class="method-section">
            <h3>💻 Algorithme de Scoring</h3>
            <div class="code-block">
# Pour chaque phrase du texte :
score = 0

# Score basé sur la fréquence des mots
for mot in phrase:
    if mot in freq_mots_globale:
        score += freq_mots_globale[mot]

# Bonus position (début/fin importants)
if position < 20% du texte:
    score += 15
elif position > 80% du texte:
    score += 10

# Bonus longueur optimale
if 50 <= longueur <= 200:
    score += 20

# Bonus mots techniques
score += nb_mots_techniques * 5

# Sélection des meilleures phrases
phrases_selectionnees = top_scored_phrases[1/4 du total]
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/telechargement/" class="btn btn-success">📤 Tester VRAI Résumé</a>
            <a href="http://127.0.0.1:8000/enregistreur/" class="btn">🎤 Tester avec Audio</a>
        </div>

        <div class="solution-section">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>ENFIN un vrai résumé !</strong> Maintenant l'application sélectionne intelligemment 
                <span class="highlight">1/4 des phrases originales</span> (résumé facile) ou 
                <span class="highlight">1/3 des phrases</span> (résumé professionnel) directement depuis votre transcription. 
                Fini les phrases inventées - vous obtenez un résumé fidèle et pertinent de votre contenu ! 🎉
            </p>
            
            <p style="color: #2e7d32; font-weight: 500; font-size: 1rem; margin: 20px 0 0 0; text-align: center;">
                <strong>Testez maintenant - vous verrez VOS phrases dans le résumé !</strong>
            </p>
        </div>
    </div>
</body>
</html>
