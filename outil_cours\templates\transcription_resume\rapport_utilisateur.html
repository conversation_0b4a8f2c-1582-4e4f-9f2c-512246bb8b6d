<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Rapport d'Évaluation Utilisateur - Administration</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            line-height: 1.6;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Styles pour la navbar (intégrés) */
        .navbar-uniforme {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
        }

        .navbar-nav {
            display: flex;
            list-style: none;
            gap: 30px;
            align-items: center;
            margin: 0;
            padding: 0;
        }

        .nav-link {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 600;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateY(-2px);
        }

        .nav-link.admin {
            background: linear-gradient(135deg, #f44336, #e91e63);
            color: white;
            padding: 10px 20px;
        }

        .nav-link.admin:hover {
            background: linear-gradient(135deg, #d32f2f, #c2185b);
            transform: translateY(-2px) scale(1.05);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .logout-form {
            display: inline;
            margin: 0;
        }

        .logout-button {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            font-size: inherit;
        }

        .logout-button:hover {
            background: linear-gradient(135deg, #475569, #334155);
            transform: translateY(-2px);
        }

        .mobile-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .mobile-toggle {
                display: block;
            }

            .navbar-nav {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: rgba(255, 255, 255, 0.98);
                flex-direction: column;
                gap: 10px;
                padding: 20px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                transform: translateY(-100%);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .navbar-nav.mobile-active {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }

            .nav-link {
                width: 100%;
                text-align: center;
                padding: 12px 20px;
            }
        }

        /* Container principal */
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .report-header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #ff9800, #f57c00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .report-subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }

        /* Formulaire */
        .form-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }

        .section-content {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #1e293b;
            font-size: 1rem;
        }

        .form-select, .form-textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
            line-height: 1.6;
        }

        /* Boutons d'action */
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8, #667eea);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #218838, #1e7e34);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
            box-shadow: 0 4px 15px rgba(100, 116, 139, 0.3);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #475569, #334155);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
        }

        /* Progression badges */
        .progression-parfait {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
        }

        .progression-moyen {
            background: linear-gradient(135deg, #ffc107, #ff8f00);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
        }

        .progression-faible {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
        }

        /* Résumé du rapport */
        .rapport-resume {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #4caf50;
        }

        .rapport-resume h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .user-info {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }

            .report-header {
                padding: 25px;
            }

            .report-title {
                font-size: 2rem;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
                text-align: center;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'includes/navbar.html' %}

    <div class="container">
        <!-- En-tête du rapport -->
        <div class="report-header">
            <h1 class="report-title">📊 Rapport d'Évaluation Utilisateur</h1>
            <p class="report-subtitle">Créer un rapport d'évaluation personnalisé pour un utilisateur</p>
        </div>

        <!-- Formulaire de création de rapport -->
        <div class="form-section">
            <div class="section-header">
                <h2 class="section-title">📝 Formulaire d'Évaluation</h2>
                <span>Sélection et évaluation</span>
            </div>
            <div class="section-content">
                <form method="post">
                    {% csrf_token %}

                    <!-- Sélection de l'utilisateur -->
                    <div class="form-group">
                        <label for="utilisateur_id" class="form-label">👤 Sélectionner un utilisateur :</label>
                        <select id="utilisateur_id" name="utilisateur_id" class="form-select" required>
                            <option value="">-- Choisir un utilisateur --</option>
                            {% for utilisateur in utilisateurs %}
                                <option value="{{ utilisateur.id }}"
                                    {% if utilisateur_selectionne and utilisateur.id == utilisateur_selectionne.id %}selected{% endif %}>
                                    {{ utilisateur.username }} ({{ utilisateur.email|default:"Email non renseigné" }})
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Évaluation de la progression -->
                    <div class="form-group">
                        <label for="progression" class="form-label">📈 Évaluation de la progression :</label>
                        <select id="progression" name="progression" class="form-select" required>
                            <option value="">-- Choisir une évaluation --</option>
                            <option value="parfait" {% if progression_selectionnee == 'parfait' %}selected{% endif %}>
                                ✅ Parfait - Excellente maîtrise
                            </option>
                            <option value="moyen" {% if progression_selectionnee == 'moyen' %}selected{% endif %}>
                                ⚠️ Moyen - Quelques améliorations nécessaires
                            </option>
                            <option value="faible" {% if progression_selectionnee == 'faible' %}selected{% endif %}>
                                ❌ Faible - Efforts supplémentaires requis
                            </option>
                        </select>
                    </div>

                    <!-- Commentaire -->
                    <div class="form-group">
                        <label for="commentaire" class="form-label">💬 Commentaire détaillé :</label>
                        <textarea
                            id="commentaire"
                            name="commentaire"
                            class="form-textarea"
                            placeholder="Ajoutez vos observations détaillées, points forts, axes d'amélioration, recommandations..."
                            required
                        >{{ commentaire_saisi }}</textarea>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="action-buttons">
                        <button type="submit" class="btn btn-primary">
                            💾 Générer le Rapport
                        </button>
                        <button type="submit" name="envoyer_email" value="1" class="btn btn-success">
                            📧 Envoyer à l'Utilisateur
                        </button>
                        <button type="submit" name="telecharger_pdf" value="1" class="btn btn-secondary">
                            📄 Télécharger PDF
                        </button>
                        <a href="{% url 'admin_transcriptions' %}" class="btn btn-secondary">
                            ⬅️ Retour Administration
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Message de confirmation d'envoi -->
        {% if email_envoye %}
        <div class="rapport-resume" style="background: linear-gradient(135deg, #e8f5e8, #c8e6c9); border-left-color: #4caf50;">
            <h3 style="color: #2e7d32;">✅ Email Envoyé avec Succès !</h3>
            <div style="background: white; border-radius: 15px; padding: 20px; margin: 15px 0;">
                <p style="color: #2e7d32; font-weight: 600; margin: 0;">
                    📧 Le rapport d'évaluation a été envoyé par email à <strong>{{ utilisateur_selectionne.username }}</strong>
                    à l'adresse : <strong>{{ utilisateur_selectionne.email }}</strong>
                </p>
                <p style="color: #4a5568; margin: 10px 0 0 0; font-size: 0.9rem;">
                    L'utilisateur recevra le rapport PDF en pièce jointe avec un message personnalisé selon son évaluation.
                </p>
            </div>
        </div>
        {% elif email_envoye == False %}
        <div class="rapport-resume" style="background: linear-gradient(135deg, #fee2e2, #fecaca); border-left-color: #dc3545;">
            <h3 style="color: #dc3545;">❌ Erreur lors de l'Envoi</h3>
            <div style="background: white; border-radius: 15px; padding: 20px; margin: 15px 0;">
                <p style="color: #dc3545; font-weight: 600; margin: 0;">
                    L'envoi de l'email a échoué. Vérifiez que l'utilisateur a une adresse email valide.
                </p>
                <p style="color: #4a5568; margin: 10px 0 0 0; font-size: 0.9rem;">
                    Email de {{ utilisateur_selectionne.username }} : {{ utilisateur_selectionne.email|default:"Non renseigné" }}
                </p>
            </div>
        </div>
        {% endif %}

        <!-- Affichage du rapport généré -->
        {% if rapport_genere and utilisateur_selectionne %}
        <div class="rapport-resume">
            <h3>📋 Aperçu du Rapport Généré</h3>

            <div class="user-info">
                <h4 style="color: #1e293b; margin-bottom: 15px;">👤 Informations Utilisateur</h4>
                <p><strong>Nom d'utilisateur :</strong> {{ utilisateur_selectionne.username }}</p>
                <p><strong>Email :</strong> {{ utilisateur_selectionne.email|default:"Non renseigné" }}</p>
                <p><strong>Inscrit le :</strong> {{ utilisateur_selectionne.date_joined|date:"d/m/Y à H:i" }}</p>
                <p><strong>Dernière connexion :</strong> {{ utilisateur_selectionne.last_login|date:"d/m/Y à H:i"|default:"Jamais connecté" }}</p>
            </div>

            <div class="user-info">
                <h4 style="color: #1e293b; margin-bottom: 15px;">📊 Statistiques d'Activité</h4>
                <p><strong>📝 Transcriptions créées :</strong> {{ utilisateur_selectionne.transcription_set.count }}</p>
                <p><strong>📋 Résumés générés :</strong> {{ utilisateur_selectionne.resume_set.count }}</p>
                <p><strong>📁 Fichiers téléversés :</strong> {{ utilisateur_selectionne.fichier_set.count }}</p>
                <p><strong>🔍 Analyses effectuées :</strong> {{ utilisateur_selectionne.motscles_set.count }}</p>
            </div>

            <div class="user-info">
                <h4 style="color: #1e293b; margin-bottom: 15px;">🎯 Évaluation</h4>
                <p><strong>Progression :</strong>
                    <span class="progression-{{ progression_selectionnee }}">
                        {% if progression_selectionnee == 'parfait' %}
                            ✅ PARFAIT
                        {% elif progression_selectionnee == 'moyen' %}
                            ⚠️ MOYEN
                        {% else %}
                            ❌ FAIBLE
                        {% endif %}
                    </span>
                </p>
                <p><strong>Commentaire :</strong></p>
                <div style="background: #f1f5f9; padding: 15px; border-radius: 8px; margin-top: 10px; border-left: 4px solid #667eea;">
                    {{ commentaire_saisi|linebreaks }}
                </div>
            </div>

            <div style="text-align: center; margin-top: 25px;">
                <form method="post" style="display: inline;">
                    {% csrf_token %}
                    <input type="hidden" name="utilisateur_id" value="{{ utilisateur_selectionne.id }}">
                    <input type="hidden" name="progression" value="{{ progression_selectionnee }}">
                    <input type="hidden" name="commentaire" value="{{ commentaire_saisi }}">
                    <button type="submit" name="envoyer_email" value="1" class="btn btn-success">
                        📧 Envoyer à {{ utilisateur_selectionne.username }}
                    </button>
                    <button type="submit" name="telecharger_pdf" value="1" class="btn btn-secondary">
                        📄 Télécharger PDF
                    </button>
                </form>
            </div>
        </div>
        {% endif %}

        <!-- Instructions -->
        {% if not rapport_genere %}
        <div class="form-section">
            <div class="section-header">
                <h2 class="section-title">ℹ️ Instructions</h2>
                <span>Comment utiliser</span>
            </div>
            <div class="section-content">
                <div style="color: #4a5568; line-height: 1.8;">
                    <h4 style="color: #1e293b; margin-bottom: 15px;">📋 Étapes pour créer un rapport :</h4>
                    <ol style="padding-left: 25px;">
                        <li><strong>Sélectionnez un utilisateur</strong> dans la liste déroulante</li>
                        <li><strong>Évaluez sa progression</strong> : Parfait, Moyen ou Faible</li>
                        <li><strong>Ajoutez un commentaire détaillé</strong> avec vos observations</li>
                        <li><strong>Cliquez sur "Générer le Rapport"</strong> pour voir l'aperçu</li>
                        <li><strong>Envoyez par email</strong> directement à l'utilisateur ou <strong>téléchargez le PDF</strong></li>
                    </ol>

                    <h4 style="color: #1e293b; margin: 20px 0 15px 0;">📧 Envoi par Email :</h4>
                    <ul style="padding-left: 25px;">
                        <li><strong>Envoi automatique</strong> à l'adresse email de l'utilisateur</li>
                        <li><strong>Message personnalisé</strong> selon l'évaluation (Parfait/Moyen/Faible)</li>
                        <li><strong>PDF en pièce jointe</strong> avec le rapport complet</li>
                        <li><strong>Confirmation d'envoi</strong> avec adresse email de destination</li>
                        <li><strong>Gestion d'erreurs</strong> si l'utilisateur n'a pas d'email</li>
                    </ul>

                    <h4 style="color: #1e293b; margin: 20px 0 15px 0;">📄 Contenu du rapport PDF :</h4>
                    <ul style="padding-left: 25px;">
                        <li>Informations complètes de l'utilisateur</li>
                        <li>Statistiques d'activité détaillées</li>
                        <li>Évaluation de la progression avec couleurs</li>
                        <li>Commentaire personnalisé de l'administrateur</li>
                        <li>Recommandations automatiques selon l'évaluation</li>
                        <li>Signature et date de génération</li>
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</body>
</html>
