{% extends 'utilisateurs/base.html' %}

{% block title %}Résumé et Analyse - Transcription IA{% endblock %}

{% block extra_css %}
<style>
        /* Coloré/Vibrant + Académique Design System - Page Résumé & Analyse */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
                linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 100% 100%;
            background-attachment: fixed;
            min-height: 100vh;
            line-height: 1.6;
            color: #2d3748;
            padding: 40px 20px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.05"><circle cx="30" cy="30" r="2"/></g></svg>') repeat,
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 60px 60px, 20px 20px, 20px 20px;
            pointer-events: none;
            z-index: 1;
        }

        /* Titres Académiques Vibrants */
        h1 {
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3.2rem;
            font-weight: 900;
            text-align: center;
            margin-bottom: 50px;
            letter-spacing: -1px;
            animation: gradientShift 4s ease-in-out infinite;
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 1200px;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #f093fb);
            border-radius: 2px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; transform: translateX(-50%) scaleX(1); }
            50% { opacity: 1; transform: translateX(-50%) scaleX(1.2); }
        }

        h2 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 25px;
            margin-top: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: left;
            position: relative;
            padding-bottom: 15px;
            z-index: 2;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            border-radius: 2px;
        }

        h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-top: 25px;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #f093fb, #f5576c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: left;
            position: relative;
            z-index: 2;
        }

        p {
            line-height: 1.7;
            margin-bottom: 15px;
            width: 100%;
            max-width: 800px;
            text-align: left;
        }

        ul {
            list-style: disc;
            padding-left: 20px;
            width: 100%;
            max-width: 800px;
            text-align: left;
        }

        li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease, text-decoration 0.3s ease;
        }

        a:hover {
            color: var(--link-hover-color);
            text-decoration: underline;
        }

        hr {
            border: 0;
            border-top: 1px solid var(--border-color);
            margin: 30px 0;
            width: 100%;
            max-width: 800px;
        }

        .no-data {
            color: #666666;
            font-style: italic;
            text-align: center;
            padding: 10px 0;
        }

        /* Conteneurs Académiques Vibrants */
        .content-container {
            margin-bottom: 40px;
            padding: 40px;
            background: linear-gradient(145deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(255, 255, 255, 0.85) 100%);
            border-radius: 20px;
            box-shadow:
                0 20px 40px rgba(0,0,0,0.1),
                0 8px 16px rgba(0,0,0,0.05),
                inset 0 1px 0 rgba(255,255,255,0.8);
            border: 2px solid rgba(255,255,255,0.3);
            width: 100%;
            max-width: 1200px;
            box-sizing: border-box;
            backdrop-filter: blur(20px);
            position: relative;
            z-index: 2;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .content-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg,
                #ff6b6b 0%,
                #4ecdc4 25%,
                #45b7d1 50%,
                #f093fb 75%,
                #ffd700 100%);
            background-size: 200% 100%;
            animation: colorFlow 3s ease-in-out infinite;
            border-radius: 20px 20px 0 0;
        }

        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .content-container:hover {
            transform: translateY(-8px);
            box-shadow:
                0 30px 60px rgba(0,0,0,0.15),
                0 15px 30px rgba(102, 126, 234, 0.2),
                inset 0 1px 0 rgba(255,255,255,0.9);
            border-color: rgba(255,255,255,0.6);
        }

        /* Form elements styling */
        .form-group {
            margin-bottom: 20px;
            text-align: left;
            width: 100%;
            max-width: 800px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }

        .form-group select {
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 1em;
            background-color: var(--surface-color);
            cursor: pointer;
            width: 100%;
            box-sizing: border-box;
            appearance: none; /* Remove default arrow */
            background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%234CAF50%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13%205.7L146.2%20204.6%2018.3%2075.1c-6.9-6.9-18.1-6.9-25%200-6.9%206.9-6.9%2018.1%200%2025l130.4%20129c6.9%206.9%2018.1%206.9%2025%200l130.4-129c6.9-6.9%206.9-18.1%200-25-6.8-7-17.9-7-24.8-.1z%22%2F%3E%3C%2Fsvg%3E'); /* Custom arrow */
            background-repeat: no-repeat;
            background-position: right 15px top 50%;
            background-size: 10px auto;
        }

        .form-group select:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
        }

        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            text-align: left;
            width: 100%;
            max-width: 800px;
        }

        .form-check-input {
            margin-right: 10px;
            width: 18px;
            height: 18px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            appearance: none;
            cursor: pointer;
            position: relative;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-input:checked::before {
            content: '✔';
            display: block;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
        }

        .form-check-label {
            color: var(--text-color);
            cursor: pointer;
        }

        .btn {
            display: inline-block;
            padding: 12px 25px;
            font-size: 1.1em;
            font-weight: 600;
            color: white;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none; /* Ensure no underline for button links */
            box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
        }

        .btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(231, 76, 60, 0.4);
        }

        /* Specific button styling */
        #generer-resume-btn, #analyser-texte-btn {
            margin-top: 15px;
            width: fit-content; /* Adjust width to content */
            align-self: center; /* Center buttons within their flex container */
            margin-left: auto;
            margin-right: auto;
            display: block; /* Ensure it takes full width for margin auto to work */
        }

        .actions-container {
            margin-top: 40px;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center; /* Center buttons horizontally */
            width: 100%;
            max-width: 800px;
        }

        .actions-container .btn {
            min-width: 200px; /* Ensure buttons have a minimum width */
        }

        .keyword {
            font-weight: bold;
            color: var(--primary-color);
            text-decoration: underline;
        }

        .main-idea {
            font-style: italic;
            color: var(--primary-color);
        }

        /* Chatbot Styles */
        .chatbot-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--primary-color); /* Green background */
            color: white;
            border: none;
            padding: 12px 18px;
            border-radius: 25px; /* Rounded button */
            cursor: pointer;
            z-index: 10000;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .chatbot-toggle:hover {
            background-color: var(--primary-dark); /* Slightly darker green on hover */
            transform: translateY(-2px);
        }

        .chatbox-floating {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 350px;
            background: var(--surface-color); /* White chatbox background */
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 16px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 9999;
            display: none; /* Hidden by default */
            flex-direction: column; /* For proper internal layout */
        }

        #chatbox-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--border-color);
        }

        #chatbox-header span {
            font-weight: 600;
            color: var(--primary-color); /* Green for chatbot header */
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #666;
            transition: color 0.2s ease;
        }

        .close-btn:hover {
            color: var(--error-color); /* Red on hover for close button */
        }

        .messages {
            height: 250px;
            overflow-y: auto;
            background: #f9f9f9; /* Light grey background for messages */
            padding: 12px;
            border-radius: 10px;
            margin-bottom: 12px;
            font-size: 14px;
            text-align: left;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }

        #user-input {
            width: 100%;
            padding: 12px 16px;
            font-size: 14px;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            margin-bottom: 8px;
        }

        #send-btn {
            width: 100%;
            padding: 12px;
            font-size: 14px;
            background-color: var(--primary-color); /* Green send button */
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }

        #send-btn:hover {
            background-color: var(--primary-dark); /* Slightly darker green on hover */
        }

        .chatbot-message-user {
            margin-bottom: 8px;
            color: #333;
            background-color: #e6ffe6; /* Very light green for user messages */
            padding: 8px 12px;
            border-radius: 8px;
            align-self: flex-end;
            max-width: 80%;
            word-wrap: break-word;
        }

        .chatbot-message-bot {
            margin-bottom: 8px;
            color: var(--primary-color); /* Green text for bot messages */
            background-color: white; /* White background for bot messages */
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            align-self: flex-start;
            max-width: 80%;
            word-wrap: break-word;
        }

        .chatbot-login-prompt {
            font-size: 12px;
            color: #666;
            margin-top: 12px;
            text-align: center;
            padding: 8px;
            background-color: #f3f4f6;
            border-radius: 10px;
        }

        .chatbot-login-prompt a {
            color: var(--primary-color); /* Green link in prompt */
            text-decoration: none;
            font-weight: 600;
        }

        .chatbot-login-prompt a:hover {
            text-decoration: underline;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 2em;
                margin-bottom: 20px;
            }

            h2 {
                font-size: 1.5em;
                margin-top: 20px;
            }

            h3 {
                font-size: 1.2em;
            }

            p, ul, .content-container, hr, .form-group, .form-check {
                width: 100%;
                padding-left: 0;
                box-shadow: none;
            }

            .content-container {
                padding: 20px;
                border-radius: 8px;
            }

            .form-group select,
            .form-group input[type="text"] {
                padding: 10px 12px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 1em;
                width: 100%;
                min-width: unset;
            }

            .actions-container {
                flex-direction: column;
                gap: 15px;
                align-items: center;
                margin-top: 30px;
            }

            #generer-resume-btn, #analyser-texte-btn {
                width: 100%;
            }

            .chatbot-toggle {
                bottom: 10px;
                right: 10px;
                padding: 10px 15px;
            }
            .chatbox-floating {
                width: 90%;
                right: 5%;
                bottom: 80px;
            }
        }
    </style>
{% endblock %}

{% block content %}
    <h1>Résumé et Analyse du Fichier</h1>

    <div class="content-container">
        {% if transcription %}
            <h2>Contenu du Fichier Original:</h2>
            <div id="transcription-contenu">
                <p>{{ transcription.contenu }}</p>
            </div>

            <hr>

            <div class="form-group">
                <label for="resume-type">Type de résumé :</label>
                <select id="resume-type" class="form-control">
                    <option value="facile">Facile</option>
                    <option value="professionnel">Professionnel</option>
                </select>
            </div>
            <button id="generer-resume-btn" class="btn">Générer Résumé</button>

            <button id="analyser-texte-btn" class="btn">Analyser le Texte</button>

            <div id="resume-container" class="content-container">
                <h2>Résumé:</h2>
                <div id="resume-result">
                    <p class="no-data">Cliquez sur "Générer Résumé" pour voir le résumé ici.</p>
                </div>
            </div>

            <div id="analyse-container" class="content-container">
                <h2>Analyse du Texte:</h2>
                <div>
                    <h3>Mots-clés:</h3>
                    <ul id="mots-cles-result">
                        <li class="no-data">Cliquez sur "Analyser le Texte" pour voir les mots-clés ici.</li>
                    </ul>
                </div>
                <div>
                    <h3>Idées Principales:</h3>
                    <ul id="idees-principales-result">
                        <li class="no-data">Cliquez sur "Analyser le Texte" pour voir les idées principales ici.</li>
                    </ul>
                </div>
            </div>

            <div class="actions-container">
                <button id="telecharger-resume-btn" class="btn">Télécharger le Résumé</button>
                <button id="telecharger-analyse-btn" class="btn">Télécharger l’Analyse</button>
            </div>

        {% else %}
            <p class="no-data">Aucune transcription disponible pour ce fichier.</p>
        {% endif %}
    </div>

    <p style="margin-top: 20px;"><a href="{% url 'televersement' %}">Retour à la page de téléversement</a></p>



    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const genererResumeBtn = document.getElementById('generer-resume-btn');
            const analyserTexteBtn = document.getElementById('analyser-texte-btn');
            const resumeResultDiv = document.getElementById('resume-result');
            const motsClesResultUl = document.getElementById('mots-cles-result');
            const ideesPrincipalesResultUl = document.getElementById('idees-principales-result');

            const transcriptionContenuElement = document.getElementById('transcription-contenu');
            const transcriptionContenu = transcriptionContenuElement ? transcriptionContenuElement.querySelector('p').textContent : '';

            const resumeTypeSelect = document.getElementById('resume-type');

            const csrftoken = "{{ csrf_token }}";
            const fichierId = "{{ fichier.id }}";

            // Function to reset result areas
            function resetResults() {
                resumeResultDiv.innerHTML = '<p class="no-data">Cliquez sur "Générer Résumé" pour voir le résumé ici.</p>';
                motsClesResultUl.innerHTML = '<li class="no-data">Cliquez sur "Analyser le Texte" pour voir les mots-clés ici.</li>';
                ideesPrincipalesResultUl.innerHTML = '<li class="no-data">Cliquez sur "Analyser le Texte" pour voir les idées principales ici.</li>';
            }

            resetResults(); // Call resetResults on page load

            // --- Gestion du bouton "Générer Résumé" ---
            if (genererResumeBtn) {
                genererResumeBtn.addEventListener('click', function () {
                    const selectedResumeType = resumeTypeSelect.value;
                    const useAiModel = false; // Désactiver l'IA par défaut

                    if (!transcriptionContenu) {
                        alert("Aucun contenu de transcription à résumer.");
                        return;
                    }

                    resumeResultDiv.innerHTML = '<p class="no-data">Génération du résumé en cours...</p>';

                    fetch(`/generer_resume/${fichierId}/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrftoken
                        },
                        body: JSON.stringify({
                            'texte': transcriptionContenu,
                            'resume_type': selectedResumeType,
                            'use_ai_model': useAiModel
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                let errorMessage = errData.error || 'Erreur inconnue.';
                                if (response.status === 429) {
                                    errorMessage = "Quota OpenAI dépassé ou trop de requêtes. Veuillez réessayer plus tard ou utiliser le mode local.";
                                } else if (response.status === 400) {
                                    errorMessage = `Erreur de la requête : ${errorMessage}`;
                                } else {
                                    errorMessage = `Erreur du serveur (${response.status}) : ${errorMessage}`;
                                }
                                throw new Error(errorMessage);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.resume) {
                            resumeResultDiv.textContent = data.resume;
                        } else if (data.error) {
                            resumeResultDiv.textContent = "Erreur lors de la génération du résumé: " + data.error;
                        }
                    })
                    .catch(error => {
                        console.error('Erreur lors de la requête de résumé:', error);
                        resumeResultDiv.textContent = "Une erreur est survenue lors de la demande de résumé: " + error.message;
                    });
                });
            }

            // --- Gestion du bouton "Analyser le Texte" ---
            if (analyserTexteBtn) {
                analyserTexteBtn.addEventListener('click', function () {
                    const useAiModel = false; // Désactiver l'IA par défaut

                    if (!transcriptionContenu) {
                        alert("Aucun contenu de transcription à analyser.");
                        return;
                    }

                    motsClesResultUl.innerHTML = '<li class="no-data">Analyse en cours...</li>';
                    ideesPrincipalesResultUl.innerHTML = '<li class="no-data">Analyse en cours...</li>';

                    fetch(`/analyser_texte_ajax/${fichierId}/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrftoken
                        },
                        body: JSON.stringify({
                            'texte': transcriptionContenu,
                            'use_ai_model': useAiModel
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                let errorMessage = errData.error || 'Erreur inconnue.';
                                if (response.status === 429) {
                                    errorMessage = "Quota OpenAI dépassé ou trop de requêtes. Veuillez réessayer plus tard ou utiliser le mode local.";
                                } else if (response.status === 400) {
                                    errorMessage = `Erreur de la requête : ${errorMessage}`;
                                } else {
                                    errorMessage = `Erreur du serveur (${response.status}) : ${errorMessage}`;
                                }
                                throw new Error(errorMessage);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.mots_cles && data.idees_principales) {
                            motsClesResultUl.innerHTML = data.mots_cles.map(mot => `<li><span class="keyword">${mot}</span></li>`).join('');
                            ideesPrincipalesResultUl.innerHTML = data.idees_principales.map(idee => `<li><span class="main-idea">${idee}</span></li>`).join('');
                        } else if (data.error) {
                            motsClesResultUl.innerHTML = `<li>Erreur lors de l'analyse: ${data.error}</li>`;
                            ideesPrincipalesResultUl.innerHTML = `<li></li>`;
                        }
                    })
                    .catch(error => {
                        console.error('Erreur lors de la requête d\'analyse:', error);
                        motsClesResultUl.innerHTML = `<li>Une erreur est survenue lors de la demande d'analyse: ${error.message}</li>`;
                        ideesPrincipalesResultUl.innerHTML = `<li></li>`;
                    });
                });
            }

            // --- Fonctions de téléchargement ---
            document.getElementById('telecharger-resume-btn').addEventListener('click', function () {
                const resumeTexte = resumeResultDiv.textContent.trim();
                if (resumeTexte && !resumeTexte.includes("Cliquez sur") && !resumeTexte.includes("Génération du résumé en cours...")) {
                    const blob = new Blob([resumeTexte], { type: 'text/plain;charset=utf-8' });
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = 'resume.txt';
                    link.click();
                } else {
                    alert("Aucun résumé disponible à télécharger.");
                }
            });

            document.getElementById('telecharger-analyse-btn').addEventListener('click', function () {
                const motsClesElements = motsClesResultUl.querySelectorAll('li');
                const ideesElements = ideesPrincipalesResultUl.querySelectorAll('li');

                const motsCles = Array.from(motsClesElements)
                    .map(li => li.textContent.trim())
                    .filter(text => text && !text.includes("Cliquez sur") && !text.includes("Analyse en cours..."))
                    .map(text => "- " + text)
                    .join('\n');

                const idees = Array.from(ideesElements)
                    .map(li => li.textContent.trim())
                    .filter(text => text && !text.includes("Cliquez sur") && !text.includes("Analyse en cours..."))
                    .map(text => "- " + text)
                    .join('\n');

                if (motsCles.trim() || idees.trim()) {
                    const analyseTexte = `Mots-clés:\n${motsCles}\n\nIdées principales:\n${idees}`;
                    const blob = new Blob([analyseTexte], { type: 'text/plain;charset=utf-8' });
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = 'analyse.txt';
                    link.click();
                } else {
                    alert("Aucune analyse disponible à télécharger.");
                }
            });


        });
    </script>
{% endblock %}