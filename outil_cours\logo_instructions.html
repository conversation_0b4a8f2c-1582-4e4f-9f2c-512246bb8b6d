<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Instructions Logo</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #667eea;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }
        
        .instructions-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #4caf50;
        }
        
        .instructions-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .step {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .step h4 {
            color: #2e7d32;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .sites-section {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #ff9800;
        }
        
        .sites-section h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .site-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .site-item h4 {
            color: #e65100;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .path-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .specs-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .specs-box h4 {
            color: #1565c0;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-icon">🎨</div>
            <h1 class="title">Instructions Logo</h1>
            <p class="subtitle">Comment ajouter votre logo image à l'application</p>
        </div>

        <!-- Structure préparée -->
        <div class="instructions-section">
            <h3>✅ Structure Préparée</h3>
            
            <div class="step">
                <h4>1. Dossier créé</h4>
                <p>J'ai créé le dossier pour vos images :</p>
                <div class="path-box">
                    outil_cours/transcription_resume/static/images/
                </div>
            </div>
            
            <div class="step">
                <h4>2. Code modifié</h4>
                <p>La navbar est maintenant configurée pour accepter un logo image avec fallback automatique vers l'icône si l'image n'existe pas.</p>
            </div>
            
            <div class="step">
                <h4>3. CSS ajouté</h4>
                <p>Styles pour le logo image : 40px de hauteur, coins arrondis, fond semi-transparent.</p>
            </div>
        </div>

        <!-- Sites recommandés -->
        <div class="sites-section">
            <h3>🌐 Sites Recommandés pour Créer/Télécharger un Logo</h3>
            
            <div class="site-item">
                <h4>1. Canva (Recommandé)</h4>
                <p><strong>URL :</strong> canva.com</p>
                <p><strong>Avantages :</strong> Interface simple, templates gratuits, export PNG/JPG</p>
                <p><strong>Recherche :</strong> "microphone logo" ou "audio transcription logo"</p>
            </div>
            
            <div class="site-item">
                <h4>2. Hatchful by Shopify</h4>
                <p><strong>URL :</strong> hatchful.shopify.com</p>
                <p><strong>Avantages :</strong> 100% gratuit, pas d'inscription requise</p>
                <p><strong>Catégorie :</strong> Technology ou Media</p>
            </div>
            
            <div class="site-item">
                <h4>3. LogoMaker</h4>
                <p><strong>URL :</strong> logomaker.com</p>
                <p><strong>Avantages :</strong> Version gratuite disponible, bonne qualité</p>
                <p><strong>Mots-clés :</strong> "transcription", "audio", "AI"</p>
            </div>
            
            <div class="site-item">
                <h4>4. Looka</h4>
                <p><strong>URL :</strong> looka.com</p>
                <p><strong>Avantages :</strong> IA pour générer des logos, version gratuite</p>
                <p><strong>Description :</strong> "Audio transcription AI service"</p>
            </div>
        </div>

        <!-- Spécifications -->
        <div class="specs-box">
            <h4>📏 Spécifications du Logo :</h4>
            <ul>
                <li><strong>Format :</strong> PNG (avec transparence) ou JPG</li>
                <li><strong>Taille :</strong> 200x200 pixels minimum</li>
                <li><strong>Ratio :</strong> Carré (1:1) ou légèrement rectangulaire</li>
                <li><strong>Couleurs :</strong> Qui contrastent bien avec le fond violet de la navbar</li>
                <li><strong>Style :</strong> Simple et lisible, même en petit</li>
            </ul>
        </div>

        <!-- Instructions d'installation -->
        <div class="instructions-section">
            <h3>📥 Comment Installer Votre Logo</h3>
            
            <div class="step">
                <h4>Étape 1 : Téléchargez votre logo</h4>
                <p>Créez ou téléchargez votre logo depuis un des sites recommandés.</p>
            </div>
            
            <div class="step">
                <h4>Étape 2 : Renommez le fichier</h4>
                <p>Renommez votre fichier en <span class="highlight">logo.png</span> (ou logo.jpg)</p>
            </div>
            
            <div class="step">
                <h4>Étape 3 : Placez le fichier</h4>
                <p>Copiez le fichier dans le dossier :</p>
                <div class="path-box">
                    outil_cours/transcription_resume/static/images/logo.png
                </div>
            </div>
            
            <div class="step">
                <h4>Étape 4 : Testez</h4>
                <p>Redémarrez le serveur Django et rechargez la page. Le logo apparaîtra automatiquement !</p>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="https://canva.com" class="btn btn-success" target="_blank">🎨 Créer un Logo sur Canva</a>
            <a href="https://hatchful.shopify.com" class="btn" target="_blank">🆓 Logo Gratuit Hatchful</a>
        </div>

        <div class="instructions-section">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Structure prête !</strong> Dès que vous ajoutez votre fichier <code>logo.png</code> 
                dans le dossier <code>images/</code>, il apparaîtra automatiquement dans la navbar à la place 
                de l'icône 🎤. Si le fichier n'existe pas, l'icône reste affichée comme fallback ! 🎉
            </p>
        </div>
    </div>
</body>
</html>
