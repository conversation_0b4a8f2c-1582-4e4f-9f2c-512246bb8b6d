<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Navbar - Rapport Utilisateur</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .debug-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #ff9800;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.8rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ff9800, #f57c00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.3rem;
            font-weight: 500;
        }
        
        .debug-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 4px solid #ff9800;
        }
        
        .debug-section h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .check-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
        }
        
        .check-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            width: 30px;
            text-align: center;
        }
        
        .check-text {
            flex: 1;
            font-weight: 600;
        }
        
        .solution-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #4caf50;
        }
        
        .solution-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .code-section {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #ff9800;
        }
        
        .code-section h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }
        
        .btn-warning:hover {
            background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%);
            box-shadow: 0 8px 25px rgba(255, 152, 0, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="debug-icon">🔍</div>
            <h1 class="title">Debug Navbar</h1>
            <p class="subtitle">Diagnostiquons pourquoi "Rapport Utilisateur" ne s'affiche pas</p>
        </div>

        <!-- Checklist de diagnostic -->
        <div class="debug-section">
            <h3>🔍 Diagnostic Étape par Étape</h3>
            
            <div class="check-item">
                <div class="check-icon">1️⃣</div>
                <div class="check-text">
                    <strong>Vérifiez votre statut admin :</strong><br>
                    Allez sur <code>http://127.0.0.1:8000/admin-transcriptions/</code><br>
                    ✅ Si vous y accédez → Vous êtes admin<br>
                    ❌ Si redirection → Vous n'êtes pas admin
                </div>
            </div>
            
            <div class="check-item">
                <div class="check-icon">2️⃣</div>
                <div class="check-text">
                    <strong>Vérifiez les autres liens admin :</strong><br>
                    Voyez-vous "⚙️ Administration" et "👥 Gestionnaire Utilisateurs" ?<br>
                    ✅ Si oui → La navbar admin fonctionne<br>
                    ❌ Si non → Problème de statut admin
                </div>
            </div>
            
            <div class="check-item">
                <div class="check-icon">3️⃣</div>
                <div class="check-text">
                    <strong>Testez l'URL directe :</strong><br>
                    Allez sur <code>http://127.0.0.1:8000/rapport-utilisateur/</code><br>
                    ✅ Si ça fonctionne → Le problème est dans la navbar<br>
                    ❌ Si erreur → Problème de configuration
                </div>
            </div>
            
            <div class="check-item">
                <div class="check-icon">4️⃣</div>
                <div class="check-text">
                    <strong>Videz le cache :</strong><br>
                    Appuyez sur <code>Ctrl + F5</code> ou <code>Ctrl + Shift + R</code><br>
                    Ou ouvrez une fenêtre de navigation privée
                </div>
            </div>
        </div>

        <!-- Solutions possibles -->
        <div class="solution-section">
            <h3>🔧 Solutions Possibles</h3>
            
            <h4 style="color: #2e7d32; margin: 20px 0 10px 0;">Solution 1 : Créer un superuser</h4>
            <p>Si vous n'êtes pas admin, créez un compte administrateur :</p>
            <div class="code-example">
python manage.py createsuperuser
            </div>
            
            <h4 style="color: #2e7d32; margin: 20px 0 10px 0;">Solution 2 : Vérifier la base de données</h4>
            <p>Connectez-vous à l'admin Django et vérifiez votre statut :</p>
            <div class="code-example">
http://127.0.0.1:8000/admin/
# Allez dans Users et vérifiez que "Staff status" et "Superuser status" sont cochés
            </div>
            
            <h4 style="color: #2e7d32; margin: 20px 0 10px 0;">Solution 3 : Forcer l'affichage (temporaire)</h4>
            <p>Pour tester, on peut temporairement afficher le lien pour tous les utilisateurs connectés.</p>
        </div>

        <!-- Code de debug -->
        <div class="code-section">
            <h3>🐛 Code de Debug</h3>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">Position actuelle dans navbar.html</h4>
            <div class="code-example">
{% if user.is_authenticated %}
    {% if user.is_superuser %}
        &lt;!-- Section admin --&gt;
        &lt;li&gt;&lt;a href="/admin-transcriptions/"&gt;⚙️ Administration&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="/gestionnaire-utilisateurs/"&gt;👥 Gestionnaire Utilisateurs&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="/rapport-utilisateur/"&gt;📊 Rapport Utilisateur&lt;/a&gt;&lt;/li&gt;  &lt;!-- ICI --&gt;
        &lt;li&gt;&lt;form method="post" action="{% url 'logout' %}"&gt;🚪 Déconnexion&lt;/form&gt;&lt;/li&gt;
    {% else %}
        &lt;!-- Section utilisateur normal --&gt;
    {% endif %}
{% endif %}
            </div>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">Test temporaire (pour debug)</h4>
            <div class="code-example">
&lt;!-- Ajoutez ceci temporairement pour tester --&gt;
{% if user.is_authenticated %}
    &lt;li&gt;&lt;a href="/rapport-utilisateur/" style="background: red; color: white;"&gt;
        🔴 TEST RAPPORT ({{ user.is_superuser }})
    &lt;/a&gt;&lt;/li&gt;
{% endif %}
            </div>
        </div>

        <!-- Boutons de test -->
        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/admin-transcriptions/" class="btn btn-warning">1️⃣ Test Admin</a>
            <a href="http://127.0.0.1:8000/rapport-utilisateur/" class="btn btn-warning">2️⃣ Test URL</a>
            <a href="http://127.0.0.1:8000/admin/" class="btn btn-success">3️⃣ Admin Django</a>
            <a href="http://127.0.0.1:8000/" class="btn">🏠 Accueil</a>
        </div>

        <div class="solution-section">
            <h3>🎯 Plan d'Action :</h3>
            <ol style="color: #2e7d32; font-weight: 600; padding-left: 25px;">
                <li><strong>Testez les 3 boutons ci-dessus</strong> dans l'ordre</li>
                <li><strong>Si "Test Admin" échoue</strong> → Vous n'êtes pas admin</li>
                <li><strong>Si "Test URL" fonctionne</strong> → Le problème est dans la navbar</li>
                <li><strong>Utilisez "Admin Django"</strong> pour vérifier/modifier votre statut</li>
                <li><strong>Dites-moi les résultats</strong> pour que je puisse vous aider davantage</li>
            </ol>
        </div>
    </div>
</body>
</html>
