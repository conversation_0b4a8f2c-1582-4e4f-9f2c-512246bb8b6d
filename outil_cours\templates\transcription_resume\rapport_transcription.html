<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Rapport de Transcription - Administration</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            line-height: 1.6;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Navbar */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
            align-items: center;
        }

        .nav-link {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 600;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateY(-2px);
        }

        .nav-link.admin {
            background: linear-gradient(135deg, #f44336, #e91e63);
            color: white;
            padding: 10px 20px;
        }

        .nav-link.admin:hover {
            background: linear-gradient(135deg, #d32f2f, #c2185b);
            transform: translateY(-2px) scale(1.05);
        }

        /* Container principal */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .report-header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #ff9800, #f57c00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .report-subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }

        .report-info {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 15px;
            padding: 15px 20px;
            margin-top: 20px;
            border-left: 4px solid #ff9800;
        }

        .report-info p {
            margin: 0;
            color: #e65100;
            font-size: 0.95rem;
            font-weight: 500;
        }

        /* Sections du rapport */
        .report-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }

        .section-content {
            padding: 30px;
        }

        /* Statistiques */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #5a6c7d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        /* Contenu de transcription */
        .transcription-content {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            max-height: 300px;
            overflow-y: auto;
        }

        .transcription-content h4 {
            color: #1e293b;
            margin-bottom: 15px;
        }

        .transcription-text {
            color: #4a5568;
            line-height: 1.8;
            white-space: pre-wrap;
        }

        /* Analyses et résumés */
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .analysis-card {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid #4caf50;
        }

        .analysis-card h5 {
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .analysis-content {
            color: #4a5568;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        /* Boutons d'action */
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8, #667eea);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
            box-shadow: 0 4px 15px rgba(100, 116, 139, 0.3);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #475569, #334155);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                gap: 15px;
            }

            .nav-link {
                padding: 6px 12px;
                font-size: 0.9rem;
            }

            .container {
                padding: 20px 15px;
            }

            .report-header {
                padding: 25px;
            }

            .report-title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .analysis-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="{% url 'accueil' %}" class="nav-brand">🎤 Transcription IA</a>
            <ul class="nav-links">
                <li><a href="{% url 'admin_transcriptions' %}" class="nav-link admin">⚙️ Administration</a></li>
                <li><a href="/gestionnaire-utilisateurs/" class="nav-link admin">👥 Gestionnaire Utilisateurs</a></li>
                {% if user.is_authenticated %}
                    <li>
                        <form method="post" action="{% url 'logout' %}" style="display: inline;">
                            {% csrf_token %}
                            <button type="submit" class="nav-link" style="background: none; border: none; cursor: pointer; color: inherit; font: inherit;">
                                🚪 Déconnexion
                            </button>
                        </form>
                    </li>
                {% else %}
                    <li><a href="{% url 'login' %}" class="nav-link">🔑 Connexion</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <div class="container">
        <!-- En-tête du rapport -->
        <div class="report-header">
            <h1 class="report-title">📊 Rapport de Transcription</h1>
            <p class="report-subtitle">Analyse détaillée et statistiques complètes</p>
            <div class="report-info">
                <p><strong>📅 Généré le :</strong> {{ date_rapport|date:"d/m/Y à H:i" }} • <strong>👤 Par :</strong> {{ user.username }} (Admin)</p>
            </div>
        </div>

        <!-- Informations sur la transcription -->
        <div class="report-section">
            <div class="section-header">
                <h2 class="section-title">📝 Informations de la Transcription</h2>
                <span>ID: {{ transcription.id }}</span>
            </div>
            <div class="section-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats_transcription.nb_mots }}</div>
                        <div class="stat-label">Mots</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats_transcription.nb_caracteres }}</div>
                        <div class="stat-label">Caractères</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats_transcription.nb_lignes }}</div>
                        <div class="stat-label">Lignes</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats_transcription.duree_depuis_creation }}</div>
                        <div class="stat-label">Jours depuis création</div>
                    </div>
                </div>

                <div class="transcription-content">
                    <h4>📄 Titre : {{ transcription.titre }}</h4>
                    <p><strong>📅 Date de création :</strong> {{ transcription.date_transcription|date:"d/m/Y à H:i" }}</p>
                    <p><strong>👤 Créé par :</strong> {{ transcription.utilisateur.username }}</p>
                    <h4 style="margin-top: 20px;">📝 Contenu :</h4>
                    <div class="transcription-text">{{ transcription.contenu|default:"Aucun contenu disponible" }}</div>
                </div>
            </div>
        </div>

        <!-- Informations sur l'utilisateur -->
        <div class="report-section">
            <div class="section-header">
                <h2 class="section-title">👤 Profil de l'Utilisateur</h2>
                <span>{{ utilisateur.username }}</span>
            </div>
            <div class="section-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats_utilisateur.total_transcriptions }}</div>
                        <div class="stat-label">Transcriptions</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats_utilisateur.total_resumes }}</div>
                        <div class="stat-label">Résumés</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats_utilisateur.total_fichiers }}</div>
                        <div class="stat-label">Fichiers</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats_utilisateur.total_analyses }}</div>
                        <div class="stat-label">Analyses</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats_utilisateur.membre_depuis }}</div>
                        <div class="stat-label">Jours membre</div>
                    </div>
                </div>

                <div class="transcription-content">
                    <h4>📧 Email : {{ utilisateur.email|default:"Non renseigné" }}</h4>
                    <p><strong>📅 Inscrit le :</strong> {{ utilisateur.date_joined|date:"d/m/Y à H:i" }}</p>
                    <p><strong>🕒 Dernière connexion :</strong> {{ stats_utilisateur.derniere_connexion|date:"d/m/Y à H:i"|default:"Jamais connecté" }}</p>
                    <p><strong>🔧 Statut :</strong> {% if utilisateur.is_superuser %}Administrateur{% else %}Utilisateur standard{% endif %}</p>
                </div>
            </div>
        </div>

        <!-- Analyses associées -->
        {% if analyses %}
        <div class="report-section">
            <div class="section-header">
                <h2 class="section-title">🔍 Analyses de Texte</h2>
                <span>{{ analyses.count }} analyse(s)</span>
            </div>
            <div class="section-content">
                <div class="analysis-grid">
                    {% for analyse in analyses %}
                        <div class="analysis-card">
                            <h5>🔍 Analyse #{{ analyse.id }}</h5>
                            <div class="analysis-content">
                                <p><strong>📅 Créée le :</strong> {{ analyse.date_creation|date:"d/m/Y à H:i" }}</p>
                                <p><strong>🔑 Mots-clés :</strong></p>
                                <p>{{ analyse.mots_cles_principaux|default:"Aucun mot-clé" }}</p>
                                <p><strong>💡 Idées principales :</strong></p>
                                {% if analyse.get_idees_sentences %}
                                    <ul style="margin: 10px 0; padding-left: 20px;">
                                        {% for idee in analyse.get_idees_sentences %}
                                            <li>{{ idee }}</li>
                                        {% endfor %}
                                    </ul>
                                {% else %}
                                    <p>Aucune idée principale</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Résumés liés à cette transcription -->
        {% if resumes %}
        <div class="report-section">
            <div class="section-header">
                <h2 class="section-title">📋 Résumés Liés à cette Transcription</h2>
                <span>{{ resumes|length }} résumé(s)</span>
            </div>
            <div class="section-content">
                <p style="color: #5a6c7d; margin-bottom: 20px; font-style: italic;">
                    📝 Résumés générés à partir de cette transcription spécifique
                </p>
                <div class="analysis-grid">
                    {% for resume in resumes %}
                        <div class="analysis-card" style="border-left-color: #2196f3;">
                            <h5>📋 {{ resume.titre }}</h5>
                            <div class="analysis-content">
                                <p><strong>📅 Créé le :</strong> {{ resume.date_creation|date:"d/m/Y à H:i" }}</p>
                                <p><strong>👤 Auteur :</strong> {{ resume.auteur.username }}</p>
                                <p><strong>📝 Contenu :</strong></p>
                                <div style="max-height: 150px; overflow-y: auto; background: #f1f5f9; padding: 10px; border-radius: 8px; margin-top: 10px;">
                                    {{ resume.contenu|default:"Aucun contenu" }}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% else %}
        <div class="report-section">
            <div class="section-header">
                <h2 class="section-title">📋 Résumés Liés à cette Transcription</h2>
                <span>0 résumé</span>
            </div>
            <div class="section-content">
                <div style="text-align: center; padding: 40px; color: #5a6c7d;">
                    <div style="font-size: 3rem; margin-bottom: 20px;">📋</div>
                    <h4>Aucun résumé lié</h4>
                    <p>Aucun résumé n'a été généré à partir de cette transcription.</p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Commentaire de l'administrateur -->
        <div class="report-section">
            <div class="section-header">
                <h2 class="section-title">💬 Commentaire Administrateur</h2>
                <span>Évaluation et observations</span>
            </div>
            <div class="section-content">
                <form method="post" style="margin-bottom: 20px;">
                    {% csrf_token %}
                    <div style="margin-bottom: 20px;">
                        <label for="commentaire_admin" style="display: block; margin-bottom: 10px; font-weight: 600; color: #1e293b;">
                            📝 Votre commentaire sur cette transcription :
                        </label>
                        <textarea
                            id="commentaire_admin"
                            name="commentaire_admin"
                            rows="6"
                            style="width: 100%; padding: 15px; border: 2px solid #e2e8f0; border-radius: 12px; font-family: inherit; font-size: 1rem; line-height: 1.5; resize: vertical;"
                            placeholder="Ajoutez vos observations, évaluations ou recommandations concernant cette transcription..."
                        >{{ commentaire_admin }}</textarea>
                    </div>

                    <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                        <button type="submit" class="btn btn-primary">
                            💾 Sauvegarder Commentaire
                        </button>
                        <button type="submit" name="telecharger_pdf" value="1" class="btn btn-success">
                            📄 Télécharger Rapport PDF
                        </button>
                    </div>
                </form>

                {% if commentaire_admin %}
                <div style="background: #f8fafc; border-left: 4px solid #667eea; padding: 20px; border-radius: 8px; margin-top: 20px;">
                    <h4 style="color: #1e293b; margin-bottom: 10px;">💬 Commentaire sauvegardé :</h4>
                    <p style="color: #4a5568; line-height: 1.6; margin: 0;">{{ commentaire_admin }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Boutons d'action -->
        <div class="action-buttons">
            <a href="{% url 'admin_transcriptions' %}" class="btn btn-secondary">⬅️ Retour Administration</a>
            <a href="{% url 'voir_transcription' transcription.id %}" class="btn btn-primary">👁️ Voir Transcription</a>
        </div>
    </div>
</body>
</html>
