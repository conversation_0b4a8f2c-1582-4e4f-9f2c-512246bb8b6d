<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Diagnostic Conversion Vidéo</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .diagnostic-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #ff9800;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.8rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ff9800, #f57c00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.3rem;
            font-weight: 500;
        }
        
        .error-analysis {
            background: #fee2e2;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #dc3545;
        }
        
        .error-analysis h3 {
            color: #dc3545;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .error-message {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 2px solid #dc3545;
            font-family: 'Courier New', monospace;
            color: #dc3545;
            font-weight: 600;
        }
        
        .solutions-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #4caf50;
        }
        
        .solutions-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .solution-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .solution-item h4 {
            color: #2e7d32;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .checklist {
            background: #fff3e0;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #ff9800;
        }
        
        .checklist h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .check-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
        }
        
        .check-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            width: 30px;
            text-align: center;
        }
        
        .check-text {
            flex: 1;
            font-weight: 600;
        }
        
        .code-section {
            background: linear-gradient(135deg, #f3e5f5, #e1bee7);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #9c27b0;
        }
        
        .code-section h3 {
            color: #6a1b9a;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }
        
        .btn-warning:hover {
            background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%);
            box-shadow: 0 8px 25px rgba(255, 152, 0, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="diagnostic-icon">🔧</div>
            <h1 class="title">Diagnostic Conversion</h1>
            <p class="subtitle">Résolution du problème "Fichier audio temporaire non créé"</p>
        </div>

        <!-- Analyse de l'erreur -->
        <div class="error-analysis">
            <h3>❌ Erreur Identifiée</h3>
            <div class="error-message">
                Erreur: Fichier audio temporaire non créé
            </div>
            <p><strong>Signification :</strong> L'extraction audio de votre vidéo a échoué. Ni FFmpeg ni moviepy n'ont réussi à créer le fichier audio temporaire nécessaire pour la transcription.</p>
        </div>

        <!-- Checklist de diagnostic -->
        <div class="checklist">
            <h3>🔍 Checklist de Diagnostic</h3>
            
            <div class="check-item">
                <div class="check-icon">1️⃣</div>
                <div class="check-text">
                    <strong>Votre vidéo contient-elle de l'audio ?</strong><br>
                    Ouvrez votre vidéo dans un lecteur (VLC, Windows Media Player) et vérifiez qu'il y a bien du son.
                </div>
            </div>
            
            <div class="check-item">
                <div class="check-icon">2️⃣</div>
                <div class="check-text">
                    <strong>Format de fichier supporté ?</strong><br>
                    Formats acceptés : MP4, AVI, MOV. Évitez les formats exotiques ou très récents.
                </div>
            </div>
            
            <div class="check-item">
                <div class="check-icon">3️⃣</div>
                <div class="check-text">
                    <strong>Taille du fichier raisonnable ?</strong><br>
                    Recommandé : moins de 100MB et moins de 10 minutes pour éviter les timeouts.
                </div>
            </div>
            
            <div class="check-item">
                <div class="check-icon">4️⃣</div>
                <div class="check-text">
                    <strong>Fichier non corrompu ?</strong><br>
                    Essayez de lire la vidéo complètement pour vérifier qu'elle n'est pas corrompue.
                </div>
            </div>
            
            <div class="check-item">
                <div class="check-icon">5️⃣</div>
                <div class="check-text">
                    <strong>FFmpeg installé ?</strong><br>
                    Ouvrez un terminal et tapez <code>ffmpeg -version</code> pour vérifier l'installation.
                </div>
            </div>
        </div>

        <!-- Solutions -->
        <div class="solutions-section">
            <h3>🛠️ Solutions par Ordre de Priorité</h3>
            
            <div class="solution-item">
                <h4>1. Vérifier l'Audio de la Vidéo</h4>
                <p><strong>Action :</strong> Ouvrez votre vidéo dans VLC ou un autre lecteur</p>
                <p><strong>Vérifiez :</strong> Que le son fonctionne et qu'il y a bien de la parole</p>
                <p><strong>Si pas d'audio :</strong> Votre vidéo ne contient pas de piste audio</p>
            </div>
            
            <div class="solution-item">
                <h4>2. Essayer un Fichier Plus Petit</h4>
                <p><strong>Action :</strong> Coupez votre vidéo en segments de 2-3 minutes maximum</p>
                <p><strong>Outils :</strong> VLC, FFmpeg, ou éditeurs vidéo en ligne</p>
                <p><strong>Avantage :</strong> Évite les timeouts et facilite le debugging</p>
            </div>
            
            <div class="solution-item">
                <h4>3. Convertir en MP4 Standard</h4>
                <p><strong>Action :</strong> Convertissez votre vidéo en MP4 avec codec H.264</p>
                <p><strong>Commande FFmpeg :</strong> <code>ffmpeg -i input.avi -c:v libx264 -c:a aac output.mp4</code></p>
                <p><strong>Pourquoi :</strong> Format le plus compatible avec notre système</p>
            </div>
            
            <div class="solution-item">
                <h4>4. Installer/Réinstaller FFmpeg</h4>
                <p><strong>Windows :</strong> Télécharger depuis ffmpeg.org et ajouter au PATH</p>
                <p><strong>Linux :</strong> <code>sudo apt install ffmpeg</code></p>
                <p><strong>macOS :</strong> <code>brew install ffmpeg</code></p>
            </div>
            
            <div class="solution-item">
                <h4>5. Extraire l'Audio Manuellement</h4>
                <p><strong>Action :</strong> Extraire l'audio vous-même et téléverser le fichier WAV/MP3</p>
                <p><strong>Commande :</strong> <code>ffmpeg -i video.mp4 -vn -acodec pcm_s16le -ar 16000 -ac 1 audio.wav</code></p>
                <p><strong>Avantage :</strong> Contourne complètement le problème de conversion</p>
            </div>
        </div>

        <!-- Améliorations apportées -->
        <div class="code-section">
            <h3>🚀 Améliorations Apportées au Code</h3>
            
            <h4 style="color: #6a1b9a; margin: 20px 0 10px 0;">1. Logs détaillés pour diagnostic</h4>
            <div class="code-example">
logger.info(f"📊 Taille du fichier audio: {file_size} bytes ({file_size/1024/1024:.2f} MB)")
logger.info(f"✅ Audio extrait avec FFmpeg vers: {wav_path_temp}")
logger.error("❌ Toutes les méthodes de conversion ont échoué")
            </div>
            
            <h4 style="color: #6a1b9a; margin: 20px 0 10px 0;">2. Vérifications multiples</h4>
            <div class="code-example">
# Vérifier que le fichier existe ET n'est pas vide
if os.path.exists(audio_path_temp) and os.path.getsize(audio_path_temp) > 0:
    wav_path_temp = audio_path_temp
    conversion_reussie = True
else:
    logger.error("❌ Fichier audio vide ou non créé")
            </div>
            
            <h4 style="color: #6a1b9a; margin: 20px 0 10px 0;">3. Messages d'erreur détaillés</h4>
            <div class="code-example">
contenu_fichier = """Erreur: Impossible d'extraire l'audio de cette vidéo. 
Vérifiez que:
1. La vidéo contient bien une piste audio
2. Le format vidéo est supporté (MP4, AVI, MOV)  
3. Le fichier n'est pas corrompu
4. FFmpeg est installé sur le système"""
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/telechargement/" class="btn btn-warning">🧪 Tester Conversion</a>
            <a href="http://127.0.0.1:8000/" class="btn">🏠 Retour Accueil</a>
        </div>

        <div class="solutions-section">
            <h3>🎯 Résumé :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Diagnostic amélioré</strong> : Le système fournit maintenant des messages 
                d'erreur détaillés et des logs complets pour identifier précisément pourquoi la 
                conversion échoue. Suivez les solutions dans l'ordre pour résoudre le problème !
            </p>
        </div>
    </div>
</body>
</html>
