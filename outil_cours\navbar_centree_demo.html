<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📐 Navbar Centrée</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .center-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.8rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.3rem;
            font-weight: 500;
        }
        
        .improvements-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #4caf50;
        }
        
        .improvements-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .improvement-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .improvement-item h4 {
            color: #2e7d32;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 50px 0;
        }
        
        .before-card, .after-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
        }
        
        .before-card h3 {
            background: linear-gradient(135deg, #dc3545, #c82333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .after-card h3 {
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .navbar-mockup {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 50%, rgba(240, 147, 251, 0.95) 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }
        
        .navbar-mockup.before {
            justify-content: flex-start;
            padding-left: 50px;
        }
        
        .navbar-mockup.after {
            justify-content: space-between;
            max-width: 1200px;
            margin: 15px auto;
        }
        
        .brand {
            font-weight: 700;
        }
        
        .nav-items {
            display: flex;
            gap: 10px;
            font-size: 0.8rem;
        }
        
        .code-section {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #ff9800;
        }
        
        .code-section h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .comparison {
                grid-template-columns: 1fr;
            }
            
            .navbar-mockup {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="center-icon">📐</div>
            <h1 class="title">Navbar Parfaitement Centrée</h1>
            <p class="subtitle">Alignement optimal sur toutes les tailles d'écran</p>
        </div>

        <div class="comparison">
            <div class="before-card">
                <h3>❌ AVANT - Décalage</h3>
                <div class="navbar-mockup before">
                    <div class="brand">🎤 Transcription IA</div>
                    <div class="nav-items">
                        <span>🏠 Accueil</span>
                        <span>📤 Upload</span>
                        <span>🚪 Logout</span>
                    </div>
                </div>
                <p style="color: #dc3545; text-align: center; margin-top: 15px;">
                    Navbar légèrement décalée vers la gauche
                </p>
            </div>
            
            <div class="after-card">
                <h3>✅ APRÈS - Parfaitement Centrée</h3>
                <div class="navbar-mockup after">
                    <div class="brand">🎤 Transcription IA</div>
                    <div class="nav-items">
                        <span>🏠 Accueil</span>
                        <span>📤 Upload</span>
                        <span>🚪 Logout</span>
                    </div>
                </div>
                <p style="color: #28a745; text-align: center; margin-top: 15px;">
                    Navbar parfaitement centrée avec max-width
                </p>
            </div>
        </div>

        <!-- Améliorations apportées -->
        <div class="improvements-section">
            <h3>🔧 Améliorations Apportées</h3>
            
            <div class="improvement-item">
                <h4>1. Container Centré Optimisé</h4>
                <p><strong>Ajout :</strong> <code>width: 100%</code> et <code>box-sizing: border-box</code></p>
                <p><strong>Effet :</strong> Garantit un centrage parfait sur toutes les tailles d'écran</p>
            </div>
            
            <div class="improvement-item">
                <h4>2. Navigation Centrée</h4>
                <p><strong>Ajout :</strong> <code>justify-content: center</code> et <code>flex-wrap: wrap</code></p>
                <p><strong>Effet :</strong> Les éléments de navigation sont mieux alignés</p>
            </div>
            
            <div class="improvement-item">
                <h4>3. Responsive Amélioré</h4>
                <p><strong>Ajout :</strong> <code>justify-content: center</code> et <code>text-align: center</code> en mobile</p>
                <p><strong>Effet :</strong> Centrage parfait même sur petits écrans</p>
            </div>
        </div>

        <!-- Code CSS amélioré -->
        <div class="code-section">
            <h3>💻 Code CSS Amélioré</h3>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">1. Container optimisé</h4>
            <div class="code-example">
.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    position: relative;
    z-index: 2;
    width: 100%;              /* ← AJOUTÉ */
    box-sizing: border-box;   /* ← AJOUTÉ */
}
            </div>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">2. Navigation centrée</h4>
            <div class="code-example">
.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 5px;
    align-items: center;
    justify-content: center;  /* ← AJOUTÉ */
    flex-wrap: wrap;          /* ← AJOUTÉ */
}
            </div>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">3. Responsive centré</h4>
            <div class="code-example">
@media (max-width: 768px) {
    .navbar-container {
        padding: 12px 20px;
        flex-wrap: wrap;
        justify-content: center;  /* ← AJOUTÉ */
        text-align: center;       /* ← AJOUTÉ */
    }
}
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/" class="btn btn-success">📐 Voir Navbar Centrée</a>
            <a href="http://127.0.0.1:8000/telechargement/" class="btn">📤 Tester sur Autre Page</a>
        </div>

        <div class="improvements-section">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Navbar parfaitement centrée</strong> : La navbar est maintenant 
                parfaitement alignée au centre sur toutes les pages et toutes les tailles 
                d'écran grâce aux améliorations CSS appliquées !
            </p>
        </div>
    </div>
</body>
</html>
