Cours de Python : Les Bases
1. Introduction à Python
Qu'est-ce que Python ?
Python est un langage de programmation interprété, de haut niveau et à usage général. Cela signifie qu'il est relativement facile à lire et à écrire (il ressemble presque à de l'anglais simple), et qu'il peut être utilisé pour une très grande variété de tâches.

Pourquoi apprendre Python ?

Simplicité : Sa syntaxe est claire et concise, ce qui le rend idéal pour les débutants.
Polyvalence : Utilisé dans le développement web (Django, Flask), l'analyse de données (Pandas, NumPy), l'intelligence artificielle (TensorFlow, PyTorch), l'automatisation de tâches, les jeux vidéo, et bien plus encore.
Communauté : Une communauté très active et de nombreuses ressources disponibles en ligne.
Multiplateforme : Fonctionne sur Windows, macOS, Linux.
Comment exécuter du code Python ?
Vous avez besoin d'un interpréteur Python. Vous pouvez :

Utiliser un IDE (Environnement de Développement Intégré) comme PyCharm, VS Code, ou Spyder.
Utiliser un notebook interactif comme Jupyter Notebook ou Google Colab.
Simplement ouvrir un terminal (ou invite de commande) et taper python pour entrer dans l'interpréteur interactif, ou python votre_script.py pour exécuter un fichier.
2. Votre Premier Programme : "Hello, World!"
Traditionnellement, le premier programme que l'on écrit est un simple message.

Python

# Ceci est un commentaire, il est ignoré par Python.
# Il sert à expliquer votre code.

print("Hello, World!")
Explication :

print() est une fonction intégrée en Python qui affiche la valeur passée en argument à l'écran.
"Hello, World!" est une chaîne de caractères (du texte), délimitée par des guillemets.
3. Les Variables
Une variable est comme une boîte ou un conteneur qui peut stocker une valeur. Vous pouvez nommer ces boîtes pour vous souvenir de ce qu'elles contiennent.

Python

# Création et assignation de variables
nom = "Alice"  # Une chaîne de caractères (string)
age = 30       # Un nombre entier (integer)
taille = 1.75  # Un nombre décimal (float)
est_etudiant = True # Une valeur booléenne (True/False)

print(nom)
print(age)
print(taille)
print(est_etudiant)

# Les variables peuvent changer de valeur
age = age + 1
print("Nouvel âge :", age)

# Vérifier le type d'une variable
print(type(nom))
print(type(age))
Règles de nommage des variables :

Doivent commencer par une lettre (a-z, A-Z) ou un underscore (_).
Ne peuvent pas commencer par un chiffre.
Peuvent contenir des lettres, des chiffres et des underscores.
Sont sensibles à la casse (maVariable est différent de mavariale).
Évitez d'utiliser les mots-clés réservés de Python (comme if, for, print, etc.).
4. Types de Données Fondamentaux
Python gère automatiquement les types de données, vous n'avez pas besoin de les déclarer explicitement.

int (Integer - Entier) : Nombres entiers (ex: 10, -5, 0).
float (Floating-point number - Flottant) : Nombres décimaux (ex: 3.14, -0.5, 10.0).
str (String - Chaîne de caractères) : Séquences de caractères (texte) délimitées par des guillemets simples (') ou doubles ("). (ex: "Bonjour", 'Python').
bool (Boolean - Booléen) : Représente des valeurs de vérité : True ou False. (ex: True, False).
5. Les Opérateurs
Les opérateurs sont des symboles qui effectuent des opérations sur des valeurs et des variables.

5.1 Opérateurs Arithmétiques
Opérateur	Description	Exemple	Résultat
+	Addition	5 + 3	8
-	Soustraction	10 - 4	6
*	Multiplication	7 * 2	14
/	Division	10 / 3	3.333...
//	Division entière	10 // 3	3
%	Modulo (reste)	10 % 3	1
**	Exposant	2 ** 3	8

Exporter vers Sheets
Python

resultat = (5 + 3) * 2
print(resultat) # Affiche 16
5.2 Opérateurs de Comparaison
Utilisés pour comparer des valeurs. Ils retournent True ou False.

Opérateur	Description	Exemple	Résultat
==	Égal à	5 == 5	True
!=	Différent de	5 != 10	True
<	Inférieur à	5 < 10	True
>	Supérieur à	10 > 5	True
<=	Inférieur ou égal à	5 <= 5	True
>=	Supérieur ou égal à	10 >= 5	True

Exporter vers Sheets
Python

x = 10
y = 20
print(x < y)  # Affiche True
print(x == y) # Affiche False
5.3 Opérateurs Logiques
Utilisés pour combiner des expressions booléennes.

and : True si les deux expressions sont True.
or : True si au moins une des expressions est True.
not : Inverse la valeur booléenne (True devient False, False devient True).
Python

a = True
b = False
print(a and b) # Affiche False
print(a or b)  # Affiche True
print(not a)   # Affiche False
6. L'Entrée Utilisateur (input())
Vous pouvez demander à l'utilisateur de saisir des informations pendant l'exécution de votre programme.

Python

nom_utilisateur = input("Entrez votre nom : ")
print("Bonjour,", nom_utilisateur)

# Attention : input() retourne toujours une chaîne de caractères (str)
age_str = input("Quel est votre âge ? ")
age_int = int(age_str) # Convertir la chaîne en entier
print("Dans 10 ans, vous aurez", age_int + 10, "ans.")
Note importante : Si vous attendez un nombre de l'utilisateur, vous devez le convertir explicitement en int() ou float() après l'avoir reçu de input().

7. Les Conditions (if, elif, else)
Les conditions permettent à votre programme de prendre des décisions et d'exécuter un code différent en fonction de si une condition est vraie ou fausse.

Python

temperature = 25

if temperature > 30:
    print("Il fait très chaud !")
elif temperature > 20: # S'exécute si la première condition est fausse mais celle-ci est vraie
    print("Il fait bon.")
else: # S'exécute si toutes les conditions précédentes sont fausses
    print("Il fait frais.")

# Exemple avec des chaînes de caractères
mot_de_passe = "secret"
saisie = input("Entrez le mot de passe : ")

if saisie == mot_de_passe:
    print("Accès autorisé.")
else:
    print("Mot de passe incorrect.")
Indentation : L'indentation (l'espace avant le code) est CRUCIALE en Python. Elle définit les blocs de code. Une mauvaise indentation entraînera des erreurs. Utilisez 4 espaces ou une tabulation.

8. Les Boucles (for et while)
Les boucles permettent de répéter un bloc de code plusieurs fois.

8.1 Boucle for
La boucle for est utilisée pour itérer sur une séquence (comme une liste de nombres, une chaîne de caractères, etc.).

Python

# Itérer sur une séquence de nombres (générée par range())
print("Compte à rebours :")
for i in range(5): # range(5) génère 0, 1, 2, 3, 4
    print(i)

print("---")

# Itérer sur une liste de fruits
fruits = ["pomme", "banane", "cerise"]
for fruit in fruits:
    print("J'aime les", fruit)

print("---")

# Itérer sur les caractères d'une chaîne
for lettre in "Python":
    print(lettre)
8.2 Boucle while
La boucle while continue de s'exécuter tant qu'une condition donnée est vraie.

Python

compteur = 0
while compteur < 5:
    print("Compteur :", compteur)
    compteur = compteur + 1 # Incrémenter le compteur pour éviter une boucle infinie

print("Fin de la boucle while.")
9. Structures de Données Fondamentales
Python offre plusieurs structures de données intégrées très utiles.

9.1 Listes (list)
Une liste est une collection ordonnée et modifiable d'éléments. Les éléments peuvent être de types différents.

Python

# Création d'une liste
ma_liste = [10, "Python", 3.14, True]
print(ma_liste)

# Accéder aux éléments (l'indexation commence à 0)
print(ma_liste[0]) # Affiche 10
print(ma_liste[1]) # Affiche "Python"

# Modifier un élément
ma_liste[0] = 100
print(ma_liste) # Affiche [100, "Python", 3.14, True]

# Ajouter un élément
ma_liste.append("nouvel_element")
print(ma_liste)

# Supprimer un élément
ma_liste.remove(3.14)
print(ma_liste)

# Longueur de la liste
print("Nombre d'éléments :", len(ma_liste))
9.2 Tuples (tuple)
Un tuple est similaire à une liste, mais il est immuable (on ne peut pas modifier ses éléments après sa création).

Python

mon_tuple = (1, 2, "trois")
print(mon_tuple)

# Accéder aux éléments (comme les listes)
print(mon_tuple[0]) # Affiche 1

# mon_tuple[0] = 5 # Ceci provoquerait une erreur car les tuples sont immuables
9.3 Dictionnaires (dict)
Un dictionnaire est une collection non ordonnée de paires clé: valeur. Chaque clé doit être unique.

Python

# Création d'un dictionnaire
personne = {
    "nom": "Dupont",
    "prenom": "Jean",
    "age": 30,
    "ville": "Paris"
}
print(personne)

# Accéder à une valeur par sa clé
print(personne["nom"])   # Affiche "Dupont"
print(personne["age"])   # Affiche 30

# Modifier une valeur
personne["age"] = 31
print(personne)

# Ajouter une nouvelle paire clé:valeur
personne["profession"] = "Développeur"
print(personne)

# Supprimer une paire
del personne["ville"]
print(personne)
10. Les Fonctions
Une fonction est un bloc de code réutilisable qui effectue une tâche spécifique. Elles permettent d'organiser votre code et de le rendre plus modulaire.

Python

# Définir une fonction sans paramètre
def saluer():
    print("Bonjour tout le monde !")

# Appeler la fonction
saluer()

# Définir une fonction avec des paramètres
def saluer_personne(nom):
    print("Bonjour,", nom, "!")

saluer_personne("Alice")
saluer_personne("Bob")

# Définir une fonction qui retourne une valeur
def additionner(a, b):
    resultat = a + b
    return resultat # La fonction renvoie la valeur de 'resultat'

somme = additionner(5, 3)
print("La somme est :", somme)

def est_pair(nombre):
    if nombre % 2 == 0:
        return True
    else:
        return False

print(est_pair(4)) # Affiche True
print(est_pair(7)) # Affiche False