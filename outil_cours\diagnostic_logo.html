<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnostic Logo</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .diagnostic-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #667eea;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }
        
        .problem-section {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #dc3545;
        }
        
        .problem-section h3 {
            color: #dc2626;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .solution-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #4caf50;
        }
        
        .solution-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .step {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .step h4 {
            color: #2e7d32;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .path-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .checklist {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .checklist h4 {
            color: #1565c0;
            margin-bottom: 15px;
        }
        
        .checklist ul {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #e3f2fd;
        }
        
        .checklist li:before {
            content: "☐ ";
            color: #1565c0;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="diagnostic-icon">🔍</div>
            <h1 class="title">Diagnostic Logo</h1>
            <p class="subtitle">Pourquoi votre logo n'apparaît pas</p>
        </div>

        <!-- Problème identifié -->
        <div class="problem-section">
            <h3>❌ Problème Identifié</h3>
            <p><strong>Le dossier était vide !</strong> J'ai vérifié et le dossier <code>images/</code> ne contenait pas votre fichier logo.</p>
            
            <div class="checklist">
                <h4>🔍 Vérifications effectuées :</h4>
                <ul>
                    <li>Dossier <code>transcription_resume/static/images/</code> créé ✅</li>
                    <li>Code navbar modifié pour accepter le logo ✅</li>
                    <li>CSS ajouté pour styliser le logo ✅</li>
                    <li>Fichier logo présent dans le dossier ❌</li>
                </ul>
            </div>
        </div>

        <!-- Solution -->
        <div class="solution-section">
            <h3>✅ Solution : Placez Votre Logo</h3>
            
            <div class="step">
                <h4>1. Vérifiez l'emplacement exact</h4>
                <p>Votre fichier logo doit être placé exactement ici :</p>
                <div class="path-box">
                    C:\Users\<USER>\outil_cours\transcription_resume\static\images\logo.png
                </div>
            </div>
            
            <div class="step">
                <h4>2. Nom du fichier</h4>
                <p>Le fichier doit s'appeler exactement <span class="highlight">logo.png</span> ou <span class="highlight">logo.jpg</span></p>
                <p><strong>Attention :</strong> Pas d'espace, pas de majuscules, pas de caractères spéciaux !</p>
            </div>
            
            <div class="step">
                <h4>3. Après avoir placé le fichier</h4>
                <ol>
                    <li>Redémarrez le serveur Django (Ctrl+C puis <code>python manage.py runserver</code>)</li>
                    <li>Allez sur <code>http://127.0.0.1:8000/</code></li>
                    <li>Appuyez sur <span class="highlight">Ctrl+F5</span> pour forcer le rechargement</li>
                    <li>Votre logo devrait apparaître !</li>
                </ol>
            </div>
        </div>

        <!-- Checklist finale -->
        <div class="checklist">
            <h4>📋 Checklist Finale :</h4>
            <ul>
                <li>J'ai téléchargé/créé mon logo</li>
                <li>Je l'ai renommé en "logo.png"</li>
                <li>Je l'ai placé dans le bon dossier</li>
                <li>J'ai redémarré le serveur Django</li>
                <li>J'ai rechargé la page avec Ctrl+F5</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/" class="btn btn-success">🏠 Tester l'Application</a>
            <a href="https://canva.com" class="btn" target="_blank">🎨 Créer un Logo</a>
        </div>

        <div class="solution-section">
            <h3>🎯 Si ça ne marche toujours pas :</h3>
            <ol style="font-size: 1.1rem; line-height: 1.8;">
                <li><strong>Vérifiez le nom :</strong> Exactement "logo.png" (pas "Logo.png" ou "logo.PNG")</li>
                <li><strong>Vérifiez l'emplacement :</strong> Dans le dossier <code>images/</code> créé</li>
                <li><strong>Vérifiez la taille :</strong> Pas trop gros (max 1MB)</li>
                <li><strong>Essayez un autre format :</strong> Si PNG ne marche pas, essayez JPG</li>
                <li><strong>Outils de développement :</strong> F12 → Console pour voir les erreurs</li>
            </ol>
            
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 20px 0 0 0; text-align: center;">
                🎉 <strong>Une fois le fichier placé correctement, ça marchera à 100% !</strong> 🎉
            </p>
        </div>
    </div>
</body>
</html>
