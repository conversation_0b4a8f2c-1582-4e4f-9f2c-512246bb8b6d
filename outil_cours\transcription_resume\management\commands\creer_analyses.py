from django.core.management.base import BaseCommand
from transcription_resume.models import Transcription, MotsCles
from transcription_resume.views import analyser_texte_local


class Command(BaseCommand):
    help = 'Crée des analyses de texte pour toutes les transcriptions existantes'

    def add_arguments(self, parser):
        parser.add_argument(
            '--limit',
            type=int,
            default=10,
            help='Nombre maximum de transcriptions à traiter (défaut: 10)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Forcer la création même si une analyse existe déjà'
        )

    def handle(self, *args, **options):
        limit = options['limit']
        force = options['force']
        
        self.stdout.write(
            self.style.SUCCESS(f'Début de la création des analyses (limite: {limit})')
        )
        
        # Récupérer les transcriptions sans analyses ou toutes si force=True
        if force:
            transcriptions = Transcription.objects.all()[:limit]
        else:
            transcriptions = Transcription.objects.filter(
                mots_cles__isnull=True
            )[:limit]
        
        if not transcriptions:
            self.stdout.write(
                self.style.WARNING('Aucune transcription à traiter.')
            )
            return
        
        self.stdout.write(f'Traitement de {len(transcriptions)} transcription(s)...')
        
        created_count = 0
        updated_count = 0
        error_count = 0
        
        for transcription in transcriptions:
            try:
                self.stdout.write(f'Traitement: {transcription.titre[:50]}...')
                
                # Vérifier si une analyse existe déjà
                analyse_existante = MotsCles.objects.filter(
                    transcription=transcription,
                    utilisateur=transcription.utilisateur
                ).first()
                
                if analyse_existante and not force:
                    self.stdout.write(
                        self.style.WARNING(f'  Analyse déjà existante pour: {transcription.titre[:30]}')
                    )
                    continue
                
                # Analyser le contenu de la transcription
                contenu = transcription.contenu or ""
                if not contenu.strip():
                    self.stdout.write(
                        self.style.WARNING(f'  Contenu vide pour: {transcription.titre[:30]}')
                    )
                    continue
                
                # Générer l'analyse
                analyse_resultat = analyser_texte_local(contenu)
                
                mots_cles_str = ', '.join(analyse_resultat.get('mots_cles', []))
                idees_str = '\n'.join(analyse_resultat.get('idees_principales', []))
                
                if analyse_existante:
                    # Mettre à jour l'analyse existante
                    analyse_existante.mots_cles_principaux = mots_cles_str
                    analyse_existante.idees_principales = idees_str
                    analyse_existante.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'  ✅ Analyse mise à jour pour: {transcription.titre[:30]}')
                    )
                else:
                    # Créer une nouvelle analyse
                    MotsCles.objects.create(
                        transcription=transcription,
                        utilisateur=transcription.utilisateur,
                        mots_cles_principaux=mots_cles_str,
                        idees_principales=idees_str
                    )
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'  ✅ Analyse créée pour: {transcription.titre[:30]}')
                    )
                
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'  ❌ Erreur pour {transcription.titre[:30]}: {str(e)}')
                )
        
        # Résumé final
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('RÉSUMÉ:'))
        self.stdout.write(f'  📊 Analyses créées: {created_count}')
        self.stdout.write(f'  🔄 Analyses mises à jour: {updated_count}')
        self.stdout.write(f'  ❌ Erreurs: {error_count}')
        self.stdout.write(f'  📈 Total traité: {created_count + updated_count}')
        
        if created_count > 0 or updated_count > 0:
            self.stdout.write(
                self.style.SUCCESS(f'\n🎉 Succès ! {created_count + updated_count} analyse(s) traitée(s).')
            )
        else:
            self.stdout.write(
                self.style.WARNING('\n⚠️ Aucune analyse n\'a été créée ou mise à jour.')
            )
