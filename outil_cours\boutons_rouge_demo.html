<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔴 Boutons Rouge - Resume Analyse</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .red-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #e74c3c;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }
        
        .changes-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #4caf50;
        }
        
        .changes-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .demo-section {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #ff9800;
        }
        
        .demo-section h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .button-demo {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 30px 0;
        }
        
        /* Anciens boutons (bleu) */
        .btn-old {
            display: inline-block;
            padding: 12px 25px;
            font-size: 1.1em;
            font-weight: 600;
            color: white;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0, 123, 255, 0.3);
        }
        
        .btn-old:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 123, 255, 0.4);
        }
        
        /* Nouveaux boutons (rouge) */
        .btn-new {
            display: inline-block;
            padding: 12px 25px;
            font-size: 1.1em;
            font-weight: 600;
            color: white;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
        }
        
        .btn-new:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(231, 76, 60, 0.4);
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .before-card, .after-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
            text-align: center;
        }
        
        .before-card h4 {
            background: linear-gradient(135deg, #007bff, #0056b3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .after-card h4 {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            margin: 15px 0;
            overflow-x: auto;
            text-align: left;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .comparison {
                grid-template-columns: 1fr;
            }
            
            .button-demo {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="red-icon">🔴</div>
            <h1 class="title">Boutons Rouge Appliqués !</h1>
            <p class="subtitle">Nouvelle couleur pour les boutons de la page resume-analyse</p>
        </div>

        <!-- Changements appliqués -->
        <div class="changes-section">
            <h3>✅ Changements Appliqués</h3>
            <p>J'ai modifié la couleur des boutons dans la page <strong>resume-analyse</strong> :</p>
            <ul>
                <li>✅ Couleur principale : <span class="highlight">Rouge (#e74c3c)</span></li>
                <li>✅ Couleur hover : <span class="highlight">Rouge foncé (#c0392b)</span></li>
                <li>✅ Dégradé appliqué pour un effet moderne</li>
                <li>✅ Ombres ajustées avec la nouvelle couleur</li>
            </ul>
        </div>

        <!-- Démonstration -->
        <div class="demo-section">
            <h3>🎨 Démonstration des Boutons</h3>
            
            <div class="comparison">
                <div class="before-card">
                    <h4>❌ AVANT - Bleu</h4>
                    <p>Ancienne couleur des boutons :</p>
                    <div class="button-demo">
                        <button class="btn-old">Générer Résumé</button>
                        <button class="btn-old">Analyser le Texte</button>
                        <button class="btn-old">Télécharger</button>
                    </div>
                    <div class="code-block">
background: linear-gradient(135deg, #007bff, #0056b3);
box-shadow: 0 4px 10px rgba(0, 123, 255, 0.3);
                    </div>
                </div>
                
                <div class="after-card">
                    <h4>✅ APRÈS - Rouge</h4>
                    <p>Nouvelle couleur des boutons :</p>
                    <div class="button-demo">
                        <button class="btn-new">Générer Résumé</button>
                        <button class="btn-new">Analyser le Texte</button>
                        <button class="btn-new">Télécharger</button>
                    </div>
                    <div class="code-block">
background: linear-gradient(135deg, #e74c3c, #c0392b);
box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
                    </div>
                </div>
            </div>
        </div>

        <!-- Code appliqué -->
        <div class="changes-section">
            <h3>💻 Code CSS Modifié</h3>
            <p>Voici le code exact qui a été appliqué dans <code>resume_analyse.html</code> :</p>
            
            <div class="code-block">
.btn {
    display: inline-block;
    padding: 12px 25px;
    font-size: 1.1em;
    font-weight: 600;
    color: white;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
}

.btn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(231, 76, 60, 0.4);
}
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/resume-analyse/1/" class="btn btn-success">🔍 Voir Page Resume-Analyse</a>
            <a href="http://127.0.0.1:8000/" class="btn">🏠 Retour Accueil</a>
        </div>

        <div class="changes-section">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Boutons rouge appliqués avec succès !</strong> Tous les boutons de la page 
                resume-analyse (Générer Résumé, Analyser le Texte, Télécharger le Résumé, Télécharger l'Analyse) 
                sont maintenant en rouge avec un dégradé moderne et des effets hover améliorés ! 🎉
            </p>
        </div>
    </div>
</body>
</html>
