<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📐 Navbar Centrée Corrigée</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .center-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.8rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.3rem;
            font-weight: 500;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 50px 0;
        }
        
        .before-card, .after-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
        }
        
        .before-card h3 {
            background: linear-gradient(135deg, #dc3545, #c82333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .after-card h3 {
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .navbar-demo {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 50%, rgba(240, 147, 251, 0.95) 100%);
            border-radius: 15px;
            padding: 15px 20px;
            margin: 20px 0;
            color: white;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .navbar-demo.before {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .navbar-demo.after {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }
        
        .brand {
            font-size: 1.2rem;
            font-weight: 900;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .nav-links li {
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .solution-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #4caf50;
        }
        
        .solution-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .code-section {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #ff9800;
        }
        
        .code-section h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="center-icon">📐</div>
            <h1 class="title">Navbar Parfaitement Centrée !</h1>
            <p class="subtitle">Correction de l'alignement des éléments de navigation</p>
        </div>

        <div class="comparison">
            <div class="before-card">
                <h3>❌ AVANT - Décalage Visible</h3>
                
                <h4>Problème d'alignement :</h4>
                <div class="navbar-demo before">
                    <div class="brand">🎤 Transcription IA</div>
                    <ul class="nav-links">
                        <li>🏠 Accueil</li>
                        <li>📤 Téléversement</li>
                        <li>🎤 Enregistreur</li>
                        <li>📝 Mes Transcriptions</li>
                    </ul>
                </div>
                
                <p><strong>CSS problématique :</strong></p>
                <div class="code-example">
.navbar-container {
    display: flex;
    justify-content: <span class="highlight">space-between</span>; /* ❌ Problème */
    align-items: center;
    /* Logo à gauche, liens à droite */
}
                </div>
                
                <p style="color: #dc3545; text-align: center; margin-top: 15px;">
                    Éléments non centrés, décalage visible
                </p>
            </div>
            
            <div class="after-card">
                <h3>✅ APRÈS - Parfaitement Centré</h3>
                
                <h4>Alignement corrigé :</h4>
                <div class="navbar-demo after">
                    <div class="brand">🎤 Transcription IA</div>
                    <ul class="nav-links">
                        <li>🏠 Accueil</li>
                        <li>📤 Téléversement</li>
                        <li>🎤 Enregistreur</li>
                        <li>📝 Mes Transcriptions</li>
                    </ul>
                </div>
                
                <p><strong>CSS corrigé :</strong></p>
                <div class="code-example">
.navbar-container {
    display: flex;
    justify-content: <span class="highlight">center</span>; /* ✅ Corrigé */
    align-items: center;
    <span class="highlight">gap: 20px;</span> /* ✅ Espacement entre éléments */
    /* Tous les éléments centrés */
}
                </div>
                
                <p style="color: #28a745; text-align: center; margin-top: 15px;">
                    Éléments parfaitement centrés
                </p>
            </div>
        </div>

        <!-- Solution détaillée -->
        <div class="solution-section">
            <h3>🔧 Solution Appliquée</h3>
            
            <h4 style="color: #2e7d32; margin: 20px 0 10px 0;">1. Modification du Container Principal</h4>
            <ul style="color: #2e7d32; margin: 0; padding-left: 25px;">
                <li><strong>justify-content :</strong> Changé de <code>space-between</code> vers <code>center</code></li>
                <li><strong>gap :</strong> Ajouté <code>20px</code> pour l'espacement entre le logo et les liens</li>
                <li><strong>Alignement :</strong> Tous les éléments sont maintenant centrés horizontalement</li>
            </ul>
            
            <h4 style="color: #2e7d32; margin: 20px 0 10px 0;">2. Amélioration des Links de Navigation</h4>
            <ul style="color: #2e7d32; margin: 0; padding-left: 25px;">
                <li><strong>gap :</strong> Augmenté de <code>5px</code> vers <code>8px</code> pour plus d'espacement</li>
                <li><strong>justify-content :</strong> Maintenu <code>center</code> pour les liens</li>
                <li><strong>flex-wrap :</strong> Conservé pour la responsivité</li>
            </ul>
            
            <h4 style="color: #2e7d32; margin: 20px 0 10px 0;">3. Responsive Design Maintenu</h4>
            <ul style="color: #2e7d32; margin: 0; padding-left: 25px;">
                <li><strong>Mobile :</strong> Le centrage fonctionne aussi sur mobile</li>
                <li><strong>Tablette :</strong> Adaptation automatique de l'espacement</li>
                <li><strong>Desktop :</strong> Alignement parfait sur tous les écrans</li>
            </ul>
        </div>

        <!-- Code détaillé -->
        <div class="code-section">
            <h3>💻 Code CSS Corrigé</h3>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">Container principal :</h4>
            <div class="code-example">
/* Container de la navbar */
.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center; /* ✅ Centrer les éléments */
    align-items: center;
    padding: 15px 30px;
    position: relative;
    z-index: 2;
    width: 100%;
    box-sizing: border-box;
    gap: 20px; /* ✅ Espacement entre le logo et les liens */
}
            </div>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">Navigation links :</h4>
            <div class="code-example">
/* Navigation links */
.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 8px; /* ✅ Augmenté pour plus d'espacement */
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/" class="btn btn-success">🏠 Voir Navbar Centrée</a>
            <a href="http://127.0.0.1:8000/telechargement/" class="btn">📤 Tester Navigation</a>
        </div>

        <div class="solution-section">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Navbar parfaitement centrée</strong> : Les éléments de navigation sont maintenant 
                parfaitement alignés au centre de la page sur tous les écrans. Le logo et les liens de 
                navigation sont équilibrés avec un espacement optimal. Plus de décalage visible ! 🎉
            </p>
        </div>
    </div>
</body>
</html>
