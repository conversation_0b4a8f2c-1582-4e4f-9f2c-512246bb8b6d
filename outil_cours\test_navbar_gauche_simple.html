<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 Test Navbar Gauche</title>
    
    <style>
        /* Reset complet */
        * {
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        /* Navbar FORCÉE À GAUCHE */
        .navbar-test {
            position: fixed;
            top: 0;
            left: 0 !important;
            right: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 50%, rgba(240, 147, 251, 0.95) 100%);
            z-index: 1000;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .navbar-container-test {
            display: flex;
            justify-content: flex-start !important;
            align-items: center;
            padding: 15px 0 15px 10px !important; /* Très peu de padding à gauche */
            margin: 0 !important;
            width: 100% !important;
            gap: 15px !important;
        }
        
        .brand-test {
            font-size: 1.5rem;
            font-weight: 900;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        .nav-test {
            display: flex;
            list-style: none;
            margin: 0 !important;
            padding: 0 !important;
            gap: 8px;
            align-items: center;
        }
        
        .nav-test li {
            margin: 0 !important;
            padding: 0 !important;
        }
        
        .nav-test a {
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            padding: 8px 12px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            white-space: nowrap;
            margin: 0 !important;
        }
        
        .nav-test a:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .content {
            margin-top: 80px;
            padding: 20px;
        }
        
        .test-result {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .test-result.success {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }
        
        .test-result.warning {
            background: #fff3e0;
            border-color: #ff9800;
            color: #e65100;
        }
    </style>
</head>
<body>
    <!-- NAVBAR TEST FORCÉE À GAUCHE -->
    <nav class="navbar-test">
        <div class="navbar-container-test">
            <!-- Logo collé à gauche -->
            <a href="#" class="brand-test">
                <span>🎤</span>
                Transcription IA
            </a>

            <!-- Pages directement après -->
            <ul class="nav-test">
                <li><a href="#">🏠 Accueil</a></li>
                <li><a href="#">📤 Téléversement</a></li>
                <li><a href="#">🎤 Enregistreur</a></li>
                <li><a href="#">📝 Mes Transcriptions</a></li>
                <li><a href="#">👤 Mon Compte</a></li>
                <li><a href="#">ℹ️ À propos</a></li>
                <li><a href="#">📞 Contact</a></li>
                <li><a href="#" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52);">🚪 Déconnexion</a></li>
            </ul>
        </div>
    </nav>

    <div class="content">
        <h1>🔥 Test Navbar Collée à Gauche</h1>
        
        <div class="test-result success">
            <h2>✅ RÉSULTAT ATTENDU</h2>
            <p><strong>La navbar doit être COLLÉE au bord gauche de l'écran !</strong></p>
            <p>Logo "🎤 Transcription IA" tout à gauche, puis les pages directement après.</p>
        </div>
        
        <div class="test-result warning">
            <h2>🔧 CSS APPLIQUÉ</h2>
            <ul style="text-align: left; display: inline-block;">
                <li><strong>margin: 0 !important</strong> - Supprimer toutes les marges</li>
                <li><strong>padding: 15px 0 15px 10px !important</strong> - Padding minimal à gauche</li>
                <li><strong>justify-content: flex-start !important</strong> - Tout à gauche</li>
                <li><strong>left: 0 !important</strong> - Navbar collée au bord</li>
                <li><strong>width: 100% !important</strong> - Prendre toute la largeur</li>
            </ul>
        </div>
        
        <h2>📋 Instructions de Test :</h2>
        <ol>
            <li>Regardez la navbar en haut de cette page</li>
            <li>Vérifiez que le logo "🎤 Transcription IA" est COLLÉ au bord gauche</li>
            <li>Vérifiez qu'il n'y a PAS d'espace vide avant le logo</li>
            <li>Vérifiez que les pages commencent directement après le logo</li>
            <li>Si c'est bon ici, le même style doit être appliqué à votre application</li>
        </ol>
        
        <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h3>🎯 Si cette page montre le bon résultat :</h3>
            <p>Alors le CSS a été modifié correctement. Videz le cache de votre navigateur sur l'application Django :</p>
            <ol>
                <li>Appuyez sur <strong>Ctrl + F5</strong> pour forcer le rechargement</li>
                <li>Ou ouvrez les outils de développement (F12) → Network → Cochez "Disable cache" → Rechargez</li>
            </ol>
        </div>
        
        <div style="background: #f3e5f5; padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h3>🔄 Changements CSS Appliqués :</h3>
            <p><strong>Fichier :</strong> <code>transcription_resume/static/css/navbar-uniforme.css</code></p>
            <ul>
                <li>Container : <code>padding: 15px 0 15px 20px !important</code></li>
                <li>Container : <code>margin: 0 !important</code></li>
                <li>Container : <code>width: 100% !important</code></li>
                <li>Brand : <code>margin: 0 !important; padding: 0 !important</code></li>
                <li>Navbar : <code>left: 0 !important; padding: 0 !important</code></li>
            </ul>
        </div>
    </div>
</body>
</html>
