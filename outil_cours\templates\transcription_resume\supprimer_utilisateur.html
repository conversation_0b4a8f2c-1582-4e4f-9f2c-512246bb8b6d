<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗑️ Supprimer Utilisateur - Administration</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .warning-box {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border-left: 6px solid #f59e0b;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }

        .warning-icon {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .warning-title {
            color: #92400e;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .warning-text {
            color: #78350f;
            font-weight: 500;
            line-height: 1.6;
        }

        .user-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 4px solid #ef4444;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .user-details h3 {
            margin: 0;
            color: #1e293b;
            font-weight: 700;
            font-size: 1.4rem;
        }

        .user-details p {
            margin: 5px 0 0 0;
            color: #64748b;
            font-size: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e2e8f0;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 900;
            color: #ef4444;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #64748b;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .danger-section {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            border-left: 6px solid #ef4444;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }

        .danger-title {
            color: #991b1b;
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .danger-list {
            color: #7f1d1d;
            margin: 0;
            padding-left: 25px;
        }

        .danger-list li {
            margin: 8px 0;
            font-weight: 500;
        }

        .actions-section {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #e2e8f0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-cancel {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
            box-shadow: 0 4px 15px rgba(100, 116, 139, 0.3);
        }

        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
        }

        .btn-delete {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }

            .title {
                font-size: 2rem;
            }

            .user-info {
                flex-direction: column;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-section {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Inclure la navbar -->
    {% include 'includes/navbar.html' %}

    <div class="container">
        <div class="header">
            <h1 class="title">🗑️ Supprimer Utilisateur</h1>
            <p class="subtitle">Confirmation de suppression définitive</p>
        </div>

        <div class="warning-box">
            <div class="warning-icon">⚠️</div>
            <div class="warning-title">Action Irréversible</div>
            <div class="warning-text">
                Cette action supprimera définitivement l'utilisateur et toutes ses données associées. 
                Cette opération ne peut pas être annulée.
            </div>
        </div>

        <div class="user-card">
            <div class="user-info">
                <div class="user-avatar">
                    {{ utilisateur.username|first|upper }}
                </div>
                <div class="user-details">
                    <h3>{{ utilisateur.username }}</h3>
                    <p>{{ utilisateur.email|default:"Pas d'email renseigné" }}</p>
                    <p>Inscrit le {{ utilisateur.date_joined|date:"d/m/Y à H:i" }}</p>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ stats_suppression.transcriptions }}</div>
                    <div class="stat-label">Transcriptions</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ stats_suppression.resumes }}</div>
                    <div class="stat-label">Résumés</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ stats_suppression.fichiers }}</div>
                    <div class="stat-label">Fichiers</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ stats_suppression.analyses }}</div>
                    <div class="stat-label">Analyses</div>
                </div>
            </div>
        </div>

        <div class="danger-section">
            <div class="danger-title">
                🚨 Données qui seront supprimées
            </div>
            <ul class="danger-list">
                <li><strong>Compte utilisateur</strong> : Suppression définitive du compte</li>
                <li><strong>{{ stats_suppression.transcriptions }} transcription(s)</strong> : Tous les textes transcrits</li>
                <li><strong>{{ stats_suppression.resumes }} résumé(s)</strong> : Tous les résumés générés</li>
                <li><strong>{{ stats_suppression.fichiers }} fichier(s)</strong> : Tous les fichiers uploadés</li>
                <li><strong>{{ stats_suppression.analyses }} analyse(s)</strong> : Toutes les analyses de texte</li>
                <li><strong>Historique complet</strong> : Toutes les activités et données associées</li>
            </ul>
        </div>

        <div class="actions-section">
            <a href="{% url 'gestionnaire_utilisateurs' %}" class="btn btn-cancel">
                ⬅️ Annuler
            </a>
            
            <form method="post" style="display: inline;">
                {% csrf_token %}
                <button type="submit" class="btn btn-delete" 
                        onclick="return confirm('ATTENTION : Cette action est irréversible !\n\nÊtes-vous absolument certain de vouloir supprimer l\'utilisateur {{ utilisateur.username }} et toutes ses données ?\n\nTapez OUI pour confirmer.')">
                    🗑️ Supprimer Définitivement
                </button>
            </form>
        </div>
    </div>
</body>
</html>
