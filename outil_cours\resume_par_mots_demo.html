<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔢 Résumé par Nombre de MOTS</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .numbers-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }
        
        .problem-section {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #dc3545;
        }
        
        .problem-section h3 {
            color: #dc2626;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .solution-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #4caf50;
        }
        
        .solution-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .before-card, .after-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
        }
        
        .before-card h4 {
            background: linear-gradient(135deg, #dc3545, #c82333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .after-card h4 {
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .stats-box {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
            text-align: center;
        }
        
        .stats-number {
            font-size: 3rem;
            font-weight: 900;
            color: #e67e22;
            margin: 10px 0;
        }
        
        .example-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .example-box.before {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }
        
        .example-box.after {
            background: #f0f9ff;
            border-color: #bfdbfe;
            color: #1565c0;
        }
        
        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="numbers-icon">🔢</div>
            <h1 class="title">Résumé par Nombre de MOTS !</h1>
            <p class="subtitle">Enfin un vrai résumé : 1/4 des mots (facile) ou 1/2 des mots (professionnel)</p>
        </div>

        <!-- Problème résolu -->
        <div class="problem-section">
            <h3>❌ Problème ENFIN Résolu</h3>
            <p><strong>Vous aviez 100% raison !</strong> L'application ne faisait pas de résumé - elle gardait le même nombre de mots.</p>
            
            <div class="comparison">
                <div class="before-card">
                    <h4>❌ Ancien Système</h4>
                    <div class="example-box before">
Texte original: 6936 mots
↓
"Résumé": 6936 mots (identique!)
                    </div>
                    <p style="color: #dc2626; font-weight: bold;">= AUCUNE RÉDUCTION !</p>
                </div>
                
                <div class="after-card">
                    <h4>✅ Nouveau Système</h4>
                    <div class="example-box after">
Texte original: 6936 mots
↓
Résumé facile: 1734 mots (1/4)
Résumé pro: 3468 mots (1/2)
                    </div>
                    <p style="color: #28a745; font-weight: bold;">= VRAIE RÉDUCTION !</p>
                </div>
            </div>
        </div>

        <!-- Nouvelle méthode -->
        <div class="solution-section">
            <h3>✅ Nouvelle Méthode par Comptage de MOTS</h3>
            
            <div class="comparison">
                <div class="stats-box">
                    <h4>📘 Résumé Facile</h4>
                    <div class="stats-number">1/4</div>
                    <p>des mots originaux<br><span class="highlight">25% du contenu</span></p>
                </div>
                
                <div class="stats-box">
                    <h4>📕 Résumé Professionnel</h4>
                    <div class="stats-number">1/2</div>
                    <p>des mots originaux<br><span class="highlight">50% du contenu</span></p>
                </div>
            </div>
        </div>

        <!-- Algorithme -->
        <div class="solution-section">
            <h3>🧮 Algorithme de Sélection par MOTS</h3>
            
            <div style="background: white; border-radius: 15px; padding: 20px; margin: 15px 0; border-left: 4px solid #28a745;">
                <h4 style="color: #2e7d32; margin-bottom: 15px;">Étapes de l'algorithme :</h4>
                <ol>
                    <li><strong>Compter les mots totaux</strong> du texte transcrit</li>
                    <li><strong>Calculer la cible :</strong>
                        <ul>
                            <li>Facile : mots_totaux ÷ 4</li>
                            <li>Professionnel : mots_totaux ÷ 2</li>
                        </ul>
                    </li>
                    <li><strong>Scorer chaque phrase</strong> selon son importance</li>
                    <li><strong>Sélectionner les phrases</strong> par ordre de score jusqu'à atteindre la cible de mots</li>
                    <li><strong>Remettre en ordre chronologique</strong> pour maintenir la cohérence</li>
                </ol>
            </div>
        </div>

        <!-- Exemples concrets -->
        <div class="solution-section">
            <h3>📊 Exemples Concrets</h3>
            
            <div class="comparison">
                <div class="before-card">
                    <h4>📘 Exemple Résumé Facile</h4>
                    <div class="example-box">
Texte original: 2000 mots
↓
Cible: 500 mots (1/4)
↓
Sélection des meilleures phrases
jusqu'à atteindre ~500 mots
                    </div>
                </div>
                
                <div class="after-card">
                    <h4>📕 Exemple Résumé Professionnel</h4>
                    <div class="example-box">
Texte original: 2000 mots
↓
Cible: 1000 mots (1/2)
↓
Sélection des meilleures phrases
jusqu'à atteindre ~1000 mots
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs de debug -->
        <div class="solution-section">
            <h3>🔍 Logs de Debug Ajoutés</h3>
            <p>L'application affiche maintenant des statistiques précises :</p>
            
            <div class="example-box after">
📊 MOTS TOTAUX dans le texte: 6936
📊 RÉSUMÉ FACILE: 1734 mots cible (1/4 de 6936)
📊 RÉSUMÉ: 1689 mots sélectionnés sur 6936 (cible: 1734)
📊 MOTS: 1689/6936 (24.4%)
📊 PHRASES: 12/45
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/telechargement/" class="btn btn-success">📤 Tester Résumé 1/4</a>
            <a href="http://127.0.0.1:8000/telechargement/" class="btn">📕 Tester Résumé 1/2</a>
        </div>

        <div class="solution-section">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>ENFIN un vrai résumé !</strong> Maintenant l'application compte les MOTS et sélectionne 
                <span class="highlight">1/4 des mots</span> (résumé facile) ou 
                <span class="highlight">1/2 des mots</span> (résumé professionnel) du texte original. 
                Votre texte de 6936 mots devient un résumé de 1734 mots (facile) ou 3468 mots (professionnel) ! 🎉
            </p>
            
            <p style="color: #2e7d32; font-weight: 500; font-size: 1rem; margin: 20px 0 0 0; text-align: center;">
                <strong>Testez maintenant - vous verrez la vraie différence de taille !</strong>
            </p>
        </div>
    </div>
</body>
</html>
