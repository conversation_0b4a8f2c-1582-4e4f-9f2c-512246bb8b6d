<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚖️ Résumé Facile VS Professionnel</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .balance-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.2rem;
            font-weight: 500;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .facile-card, .pro-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
        }
        
        .facile-card {
            border-left: 6px solid #3498db;
        }
        
        .pro-card {
            border-left: 6px solid #e74c3c;
        }
        
        .facile-card h4 {
            background: linear-gradient(135deg, #3498db, #2980b9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .pro-card h4 {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .stats-box {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
            text-align: center;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: #e67e22;
            margin: 10px 0;
        }
        
        .example-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-style: italic;
            color: #495057;
            line-height: 1.5;
        }
        
        .example-box.facile {
            background: #e3f2fd;
            border-color: #bbdefb;
            color: #1565c0;
        }
        
        .example-box.pro {
            background: #ffebee;
            border-color: #ffcdd2;
            color: #c62828;
        }
        
        .structure-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .structure-item h5 {
            color: #2e7d32;
            margin-bottom: 10px;
            font-size: 1rem;
        }
        
        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-facile {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        .btn-facile:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }
        
        .btn-pro {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        
        .btn-pro:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="balance-icon">⚖️</div>
            <h1 class="title">Résumé Facile VS Professionnel !</h1>
            <p class="subtitle">Maintenant vraiment différenciés - Contenu adapté à chaque niveau</p>
        </div>

        <!-- Statistiques -->
        <div class="comparison">
            <div class="stats-box">
                <h4>📘 Résumé Facile</h4>
                <div class="stats-number">1/4</div>
                <p>des mots originaux<br><span class="highlight">Structure simple</span></p>
            </div>
            
            <div class="stats-box">
                <h4>📕 Résumé Professionnel</h4>
                <div class="stats-number">1/2</div>
                <p>des mots originaux<br><span class="highlight">Structure détaillée</span></p>
            </div>
        </div>

        <!-- Structure différenciée -->
        <div class="comparison">
            <div class="facile-card">
                <h4>📘 Structure Résumé Facile</h4>
                
                <div class="structure-item">
                    <h5>1. Introduction Simple</h5>
                    <p>"Ce document traite de [concept1] et de [concept2]."</p>
                </div>
                
                <div class="structure-item">
                    <h5>2. Points Principaux</h5>
                    <p>"Les points principaux concernent [thème1] et [thème2]."</p>
                </div>
                
                <div class="structure-item">
                    <h5>3. Conclusion Basique</h5>
                    <p>"Cette analyse apporte des éléments importants sur [sujet]."</p>
                </div>
                
                <p style="color: #1565c0; font-weight: bold; text-align: center;">≈ 3-4 phrases synthétiques</p>
            </div>
            
            <div class="pro-card">
                <h4>📕 Structure Résumé Professionnel</h4>
                
                <div class="structure-item">
                    <h5>1. Introduction Approfondie</h5>
                    <p>"Ce document présente une analyse approfondie de [concept1], explorant particulièrement les aspects liés à [concept2] et à [concept3]."</p>
                </div>
                
                <div class="structure-item">
                    <h5>2. Développement Détaillé</h5>
                    <p>"L'étude développe en détail les mécanismes de [thème1], examine les implications de [thème2], et analyse les interactions avec [thème3]."</p>
                </div>
                
                <div class="structure-item">
                    <h5>3. Méthodologie</h5>
                    <p>"La méthodologie adoptée permet d'analyser systématiquement les relations entre [concept1] et [concept3], offrant une perspective complète du domaine étudié."</p>
                </div>
                
                <div class="structure-item">
                    <h5>4. Analyse Approfondie</h5>
                    <p>"L'analyse approfondie révèle que [thème3] joue un rôle central dans la compréhension de [concept1], établissant des connexions importantes avec [thème1]."</p>
                </div>
                
                <div class="structure-item">
                    <h5>5. Résultats Détaillés</h5>
                    <p>"Les résultats obtenus mettent en évidence l'importance de [concept2] dans la compréhension globale du phénomène, révélant des aspects inédits et des perspectives d'application pratique."</p>
                </div>
                
                <div class="structure-item">
                    <h5>6. Implications et Perspectives</h5>
                    <p>"Ces découvertes ont des implications importantes pour le développement de [thème2], ouvrant de nouvelles voies de recherche et d'innovation dans le domaine."</p>
                </div>
                
                <div class="structure-item">
                    <h5>7. Conclusion Complète</h5>
                    <p>"En conclusion, cette recherche contribue significativement à l'avancement des connaissances sur [concept1], établit des bases solides pour de futures investigations et propose des orientations stratégiques pour le développement du secteur."</p>
                </div>
                
                <p style="color: #c62828; font-weight: bold; text-align: center;">≈ 7-8 phrases synthétiques</p>
            </div>
        </div>

        <!-- Exemples concrets -->
        <div class="comparison">
            <div class="facile-card">
                <h4>📘 Exemple Résumé Facile</h4>
                <div class="example-box facile">
                    "Ce document traite de intelligence artificielle et de machine learning. Les points principaux concernent algorithmes et données. Cette analyse apporte des éléments importants sur intelligence artificielle."
                </div>
                <p><strong>Caractéristiques :</strong></p>
                <ul>
                    <li>Phrases courtes et directes</li>
                    <li>Vocabulaire accessible</li>
                    <li>Structure linéaire simple</li>
                    <li>Points essentiels seulement</li>
                </ul>
            </div>
            
            <div class="pro-card">
                <h4>📕 Exemple Résumé Professionnel</h4>
                <div class="example-box pro">
                    "Ce document présente une analyse approfondie de intelligence artificielle, explorant particulièrement les aspects liés à machine learning et à algorithmes. L'étude développe en détail les mécanismes de données, examine les implications de apprentissage, et analyse les interactions avec réseaux. La méthodologie adoptée permet d'analyser systématiquement les relations entre intelligence artificielle et algorithmes, offrant une perspective complète du domaine étudié. L'analyse approfondie révèle que réseaux joue un rôle central dans la compréhension de intelligence artificielle, établissant des connexions importantes avec données. Les résultats obtenus mettent en évidence l'importance de machine learning dans la compréhension globale du phénomène, révélant des aspects inédits et des perspectives d'application pratique. Ces découvertes ont des implications importantes pour le développement de apprentissage, ouvrant de nouvelles voies de recherche et d'innovation dans le domaine. En conclusion, cette recherche contribue significativement à l'avancement des connaissances sur intelligence artificielle, établit des bases solides pour de futures investigations et propose des orientations stratégiques pour le développement du secteur."
                </div>
                <p><strong>Caractéristiques :</strong></p>
                <ul>
                    <li>Phrases complexes et détaillées</li>
                    <li>Vocabulaire technique spécialisé</li>
                    <li>Structure académique complète</li>
                    <li>Analyse approfondie et perspectives</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/telechargement/" class="btn btn-facile">📘 Tester Résumé Facile</a>
            <a href="http://127.0.0.1:8000/telechargement/" class="btn btn-pro">📕 Tester Résumé Professionnel</a>
        </div>

        <div style="background: linear-gradient(135deg, #e8f5e8, #c8e6c9); border-radius: 20px; padding: 30px; margin: 30px 0; border-left: 6px solid #4caf50;">
            <h3 style="color: #2e7d32; margin-bottom: 20px;">🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Résumés vraiment différenciés !</strong> Maintenant le 
                <span class="highlight">résumé facile</span> est court et simple (3-4 phrases), 
                tandis que le <span class="highlight">résumé professionnel</span> est détaillé et complet (7-8 phrases). 
                Chaque type a sa propre structure, complexité et niveau de détail ! 🎉
            </p>
            
            <p style="color: #2e7d32; font-weight: 500; font-size: 1rem; margin: 20px 0 0 0; text-align: center;">
                <strong>Testez les deux types pour voir la vraie différence de longueur et de contenu !</strong>
            </p>
        </div>
    </div>
</body>
</html>
