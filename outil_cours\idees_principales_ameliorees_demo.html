<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✨ Idées <PERSON></title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #333;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: colorFlow 4s ease-in-out infinite;
        }
        
        @keyframes colorFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .success-icon {
            font-size: 5rem;
            margin-bottom: 30px;
            animation: bounce 3s ease-in-out infinite;
            color: #28a745;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
            40% { transform: translateY(-20px) scale(1.1); }
            60% { transform: translateY(-10px) scale(1.05); }
        }
        
        .title {
            font-size: 2.8rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            color: #5a6c7d;
            font-size: 1.3rem;
            font-weight: 500;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 50px 0;
        }
        
        .before-card, .after-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
        }
        
        .before-card h3 {
            background: linear-gradient(135deg, #dc3545, #c82333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .after-card h3 {
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .idee-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #6c757d;
        }
        
        .idee-item.good {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
        }
        
        .idee-item.bad {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #fee2e2, #fecaca);
        }
        
        .improvements-section {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #4caf50;
        }
        
        .improvements-section h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .code-section {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            border-left: 6px solid #ff9800;
        }
        
        .code-section h3 {
            color: #e65100;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">✨</div>
            <h1 class="title">Idées Principales Améliorées !</h1>
            <p class="subtitle">Extraction intelligente et organisée des idées vraiment intéressantes</p>
        </div>

        <div class="comparison">
            <div class="before-card">
                <h3>❌ AVANT - Idées Basiques</h3>
                <div class="idee-item bad">• L'intelligence artificielle est importante</div>
                <div class="idee-item bad">• Il faut considérer les aspects éthiques</div>
                <div class="idee-item bad">• C'est un domaine en évolution</div>
                <div class="idee-item bad">• Les applications sont diverses</div>
                <div class="idee-item bad">• Il y a des défis à relever</div>
                <p style="color: #dc3545; text-align: center; margin-top: 15px;">
                    Idées vagues et peu informatives
                </p>
            </div>
            
            <div class="after-card">
                <h3>✅ APRÈS - Idées Intelligentes</h3>
                <div class="idee-item good">• L'apprentissage automatique utilise des réseaux de neurones artificiels pour reconnaître des motifs complexes dans les données</div>
                <div class="idee-item good">• Les algorithmes de classification supervisée atteignent 95% de précision sur les datasets d'images médicales</div>
                <div class="idee-item good">• Le traitement du langage naturel permet aux machines de comprendre et générer du texte humain</div>
                <div class="idee-item good">• Les voitures autonomes combinent vision par ordinateur et capteurs LIDAR pour naviguer en sécurité</div>
                <div class="idee-item good">• L'éthique de l'IA nécessite des frameworks de transparence et d'explicabilité des décisions algorithmiques</div>
                <p style="color: #28a745; text-align: center; margin-top: 15px;">
                    Idées concrètes, informatives et utiles
                </p>
            </div>
        </div>

        <div class="improvements-section">
            <h3>🚀 Améliorations Apportées</h3>
            
            <h4 style="color: #2e7d32; margin: 20px 0 10px 0;">1. Algorithme de Scoring Intelligent</h4>
            <ul style="color: #2e7d32; margin: 0; padding-left: 25px;">
                <li><strong>Mots-clés enrichis</strong> : 35+ catégories (concepts, importance, recherche, innovation)</li>
                <li><strong>Longueur optimale</strong> : Privilégie les phrases de 15-30 mots (idées complètes)</li>
                <li><strong>Position stratégique</strong> : Introduction, développement et conclusion</li>
                <li><strong>Connecteurs logiques</strong> : "donc", "ainsi", "en effet", "notamment"</li>
                <li><strong>Indicateurs d'importance</strong> : "il faut", "essentiel", "nécessaire"</li>
            </ul>
            
            <h4 style="color: #2e7d32; margin: 20px 0 10px 0;">2. Filtrage Anti-Similarité</h4>
            <ul style="color: #2e7d32; margin: 0; padding-left: 25px;">
                <li><strong>Détection de doublons</strong> : Évite les idées trop similaires (60% de mots communs)</li>
                <li><strong>Diversité garantie</strong> : Chaque idée apporte une information unique</li>
                <li><strong>Ordre logique</strong> : Maintient l'ordre d'apparition dans le texte</li>
            </ul>
            
            <h4 style="color: #2e7d32; margin: 20px 0 10px 0;">3. Prompt OpenAI Optimisé</h4>
            <ul style="color: #2e7d32; margin: 0; padding-left: 25px;">
                <li><strong>Critères stricts</strong> : Phrases complètes, informatives, concrètes</li>
                <li><strong>Anti-généralités</strong> : Évite "c'est important", privilégie les faits</li>
                <li><strong>Ordre d'importance</strong> : Idées classées par pertinence décroissante</li>
                <li><strong>Format optimisé</strong> : Sujet-verbe-complément, 15-30 mots</li>
            </ul>
        </div>

        <div class="code-section">
            <h3>🔧 Améliorations Techniques</h3>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">1. Nouveau prompt OpenAI</h4>
            <div class="code-example">
prompt_idees = f"""Extrais 5-6 idées principales VRAIMENT intéressantes et utiles.

Critères STRICTS :
- Phrase complète, claire et informative (15-30 mots)
- Information concrète, concept clé ou découverte importante
- Directement utile pour comprendre le sujet principal
- Éviter les généralités comme "c'est important"
- Privilégier faits, méthodes, résultats, conclusions
- Organisée par ordre d'importance

Format : Une idée par ligne, phrases complètes, ordre décroissant"""
            </div>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">2. Algorithme anti-similarité</h4>
            <div class="code-example">
# Filtrer pour éviter les phrases trop similaires
for sentence, score in sorted_sentences:
    is_similar = False
    for existing_sentence in idees_principales:
        words_current = set(word_tokenize(sentence.lower()))
        words_existing = set(word_tokenize(existing_sentence.lower()))
        common_words = words_current.intersection(words_existing)
        
        # Si plus de 60% des mots sont en commun = similaire
        if len(common_words) > 0.6 * min(len(words_current), len(words_existing)):
            is_similar = True
            break
    
    if not is_similar:
        idees_principales.append(sentence)
            </div>
            
            <h4 style="color: #e65100; margin: 20px 0 10px 0;">3. Scoring enrichi</h4>
            <div class="code-example">
# Mots-clés enrichis par catégories
mots_importants = {
    # Concepts académiques
    'concept', 'théorie', 'principe', 'méthode', 'technique',
    # Importance et priorité  
    'important', 'essentiel', 'principal', 'fondamental', 'crucial',
    # Recherche et analyse
    'résultat', 'conclusion', 'analyse', 'étude', 'recherche',
    # Innovation et évolution
    'innovation', 'développement', 'évolution', 'transformation'
}

# Connecteurs logiques avancés
mots_transition_forts = {
    'donc', 'ainsi', 'par conséquent', 'en conclusion', 'finalement',
    'en effet', 'notamment', 'particulièrement', 'surtout'
}
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://127.0.0.1:8000/telechargement/" class="btn btn-success">🧪 Tester l'Analyse</a>
            <a href="http://127.0.0.1:8000/" class="btn">🏠 Retour Accueil</a>
        </div>

        <div class="improvements-section">
            <h3>🎯 Résultat Final :</h3>
            <p style="color: #2e7d32; font-weight: 600; font-size: 1.1rem; margin: 0;">
                ✅ <strong>Idées principales intelligentes</strong> : L'algorithme extrait maintenant 
                des idées vraiment intéressantes, concrètes et informatives, organisées par importance 
                et sans redondance. Fini les phrases vagues comme "c'est important" !
            </p>
        </div>
    </div>
</body>
</html>
